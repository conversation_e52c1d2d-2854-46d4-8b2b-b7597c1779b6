version: "2"
overrides:
  go:
    overrides:
      - db_type: "timestamptz"
        go_type:
          import: "time"
          type: "Time"
      - db_type: "timestamp"
        go_type:
          import: "time"
          type: "Time"
      - db_type: "timestamptz"
        go_type:
          import: "time"
          type: "Time"
          pointer: true
        nullable: true
      - db_type: "timestamp"
        go_type:
          import: "time"
          type: "Time"
          pointer: true
        nullable: true
sql:
  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/users.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "usersqueries"
        out: "internal/db/sqlc/tables/users"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/accounts.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "accountsqueries"
        out: "internal/db/sqlc/tables/accounts"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/session.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "sessionqueries"
        out: "internal/db/sqlc/tables/session"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/verificationcodes.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "verificationcodesqueries"
        out: "internal/db/sqlc/tables/verificationcodes"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/categories.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "categoriesqueries"
        out: "internal/db/sqlc/tables/categories"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/vtuberprofiles.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "vtuberprofilesqueries"
        out: "internal/db/sqlc/tables/vtuberprofiles"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/posts.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "postsqueries"
        out: "internal/db/sqlc/tables/posts"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/events.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "eventsqueries"
        out: "internal/db/sqlc/tables/events"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/campaigns.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "campaignsqueries"
        out: "internal/db/sqlc/tables/campaigns"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/eventcomments.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "eventcommentsqueries"
        out: "internal/db/sqlc/tables/eventcomments"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/vtuberrequests.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "vtuberrequestsqueries"
        out: "internal/db/sqlc/tables/vtuberrequests"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/postcomments.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "postcommentsqueries"
        out: "internal/db/sqlc/tables/postcomments"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/postlikes.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "postlikesqueries"
        out: "internal/db/sqlc/tables/postlikes"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/campaignvariants.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "campaignvariantsqueries"
        out: "internal/db/sqlc/tables/campaignvariants"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/campaignbanners.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "campaignbannersqueries"
        out: "internal/db/sqlc/tables/campaignbanners"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/vtuberplans.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "vtuberplansqueries"
        out: "internal/db/sqlc/tables/vtuberplans"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/vtuberusersubscriptions.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "vtuberusersubscriptionsqueries"
        out: "internal/db/sqlc/tables/vtuberusersubscriptions"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/faqs.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "faqsqueries"
        out: "internal/db/sqlc/tables/faqs"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/notificationtemplates.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "notificationtemplatesqueries"
        out: "internal/db/sqlc/tables/notificationtemplates"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/notifications.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "notificationsqueries"
        out: "internal/db/sqlc/tables/notifications"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/eventparticipants.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "eventparticipantsqueries"
        out: "internal/db/sqlc/tables/eventparticipants"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/eventparticipantvotes.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "eventparticipantvotesqueries"
        out: "internal/db/sqlc/tables/eventparticipantvotes"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/campaignvariantsubscriptions.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "campaignvariantsubscriptionsqueries"
        out: "internal/db/sqlc/tables/campaignvariantsubscriptions"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/userbillinginfos.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "userbillinginfosqueries"
        out: "internal/db/sqlc/tables/userbillinginfos"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/transactions.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "transactionsqueries"
        out: "internal/db/sqlc/tables/transactions"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/userpoints.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "userpointsqueries"
        out: "internal/db/sqlc/tables/userpoints"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/staticdata.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "staticdataqueries"
        out: "internal/db/sqlc/tables/staticdata"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/favoritevtubers.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "favoritevtubersqueries"
        out: "internal/db/sqlc/tables/favoritevtubers"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/favoritecampaigns.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "favoritecampaignsqueries"
        out: "internal/db/sqlc/tables/favoritecampaigns"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/files.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "filesqueries"
        out: "internal/db/sqlc/tables/files"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/announcements.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "announcementsqueries"
        out: "internal/db/sqlc/tables/announcements"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/vtuberbanners.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "vtuberbannersqueries"
        out: "internal/db/sqlc/tables/vtuberbanners"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/vtubergalleries.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "vtubergalleriesqueries"
        out: "internal/db/sqlc/tables/vtubergalleries"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true

  - engine: "postgresql"
    queries: "internal/db/sqlc/queries/campaigncategories.sql"
    schema: "internal/db/migrations/v2"
    gen:
      go:
        package: "campaigncategoriesqueries"
        out: "internal/db/sqlc/tables/campaigncategories"
        sql_package: "pgx/v5"
        omit_unused_structs: true
        emit_pointers_for_null_types: true
