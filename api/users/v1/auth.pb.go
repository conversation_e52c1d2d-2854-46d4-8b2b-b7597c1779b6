// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: users/v1/auth.proto

package usersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SignInWithSocialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Provider      string                 `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInWithSocialRequest) Reset() {
	*x = SignInWithSocialRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInWithSocialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInWithSocialRequest) ProtoMessage() {}

func (x *SignInWithSocialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInWithSocialRequest.ProtoReflect.Descriptor instead.
func (*SignInWithSocialRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{0}
}

func (x *SignInWithSocialRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *SignInWithSocialRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type SignInWithSocialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInWithSocialResponse) Reset() {
	*x = SignInWithSocialResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInWithSocialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInWithSocialResponse) ProtoMessage() {}

func (x *SignInWithSocialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInWithSocialResponse.ProtoReflect.Descriptor instead.
func (*SignInWithSocialResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{1}
}

func (x *SignInWithSocialResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *SignInWithSocialResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type GetSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSessionRequest) Reset() {
	*x = GetSessionRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionRequest) ProtoMessage() {}

func (x *GetSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionRequest.ProtoReflect.Descriptor instead.
func (*GetSessionRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{2}
}

type GetSessionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Vtuber        *v1.VtuberProfile      `protobuf:"bytes,2,opt,name=vtuber,proto3" json:"vtuber,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSessionResponse) Reset() {
	*x = GetSessionResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionResponse) ProtoMessage() {}

func (x *GetSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionResponse.ProtoReflect.Descriptor instead.
func (*GetSessionResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{3}
}

func (x *GetSessionResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GetSessionResponse) GetVtuber() *v1.VtuberProfile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

type SignOutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignOutRequest) Reset() {
	*x = SignOutRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignOutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignOutRequest) ProtoMessage() {}

func (x *SignOutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignOutRequest.ProtoReflect.Descriptor instead.
func (*SignOutRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{4}
}

type SignOutResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignOutResponse) Reset() {
	*x = SignOutResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignOutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignOutResponse) ProtoMessage() {}

func (x *SignOutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignOutResponse.ProtoReflect.Descriptor instead.
func (*SignOutResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{5}
}

func (x *SignOutResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SignOutResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SendSignupEmailVerificationCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSignupEmailVerificationCodeRequest) Reset() {
	*x = SendSignupEmailVerificationCodeRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSignupEmailVerificationCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSignupEmailVerificationCodeRequest) ProtoMessage() {}

func (x *SendSignupEmailVerificationCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSignupEmailVerificationCodeRequest.ProtoReflect.Descriptor instead.
func (*SendSignupEmailVerificationCodeRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{6}
}

func (x *SendSignupEmailVerificationCodeRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type SendSignupEmailVerificationCodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSignupEmailVerificationCodeResponse) Reset() {
	*x = SendSignupEmailVerificationCodeResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSignupEmailVerificationCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSignupEmailVerificationCodeResponse) ProtoMessage() {}

func (x *SendSignupEmailVerificationCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSignupEmailVerificationCodeResponse.ProtoReflect.Descriptor instead.
func (*SendSignupEmailVerificationCodeResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{7}
}

func (x *SendSignupEmailVerificationCodeResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SendSignupEmailVerificationCodeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type VerifySignupEmailRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Email            string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	VerificationCode string                 `protobuf:"bytes,2,opt,name=verification_code,json=verificationCode,proto3" json:"verification_code,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *VerifySignupEmailRequest) Reset() {
	*x = VerifySignupEmailRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifySignupEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySignupEmailRequest) ProtoMessage() {}

func (x *VerifySignupEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySignupEmailRequest.ProtoReflect.Descriptor instead.
func (*VerifySignupEmailRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{8}
}

func (x *VerifySignupEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *VerifySignupEmailRequest) GetVerificationCode() string {
	if x != nil {
		return x.VerificationCode
	}
	return ""
}

type VerifySignupEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifySignupEmailResponse) Reset() {
	*x = VerifySignupEmailResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifySignupEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySignupEmailResponse) ProtoMessage() {}

func (x *VerifySignupEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySignupEmailResponse.ProtoReflect.Descriptor instead.
func (*VerifySignupEmailResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{9}
}

func (x *VerifySignupEmailResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type SignupWithEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FullName      string                 `protobuf:"bytes,1,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	DateOfBirth   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3,oneof" json:"date_of_birth,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignupWithEmailRequest) Reset() {
	*x = SignupWithEmailRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignupWithEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignupWithEmailRequest) ProtoMessage() {}

func (x *SignupWithEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignupWithEmailRequest.ProtoReflect.Descriptor instead.
func (*SignupWithEmailRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{10}
}

func (x *SignupWithEmailRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *SignupWithEmailRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SignupWithEmailRequest) GetDateOfBirth() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *SignupWithEmailRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type SignupWithEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignupWithEmailResponse) Reset() {
	*x = SignupWithEmailResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignupWithEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignupWithEmailResponse) ProtoMessage() {}

func (x *SignupWithEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignupWithEmailResponse.ProtoReflect.Descriptor instead.
func (*SignupWithEmailResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{11}
}

func (x *SignupWithEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SignupWithEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SignInWithEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInWithEmailRequest) Reset() {
	*x = SignInWithEmailRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInWithEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInWithEmailRequest) ProtoMessage() {}

func (x *SignInWithEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInWithEmailRequest.ProtoReflect.Descriptor instead.
func (*SignInWithEmailRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{12}
}

func (x *SignInWithEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SignInWithEmailRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type SignInWithEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInWithEmailResponse) Reset() {
	*x = SignInWithEmailResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInWithEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInWithEmailResponse) ProtoMessage() {}

func (x *SignInWithEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInWithEmailResponse.ProtoReflect.Descriptor instead.
func (*SignInWithEmailResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{13}
}

func (x *SignInWithEmailResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *SignInWithEmailResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type SendForgotPasswordEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendForgotPasswordEmailRequest) Reset() {
	*x = SendForgotPasswordEmailRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendForgotPasswordEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendForgotPasswordEmailRequest) ProtoMessage() {}

func (x *SendForgotPasswordEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendForgotPasswordEmailRequest.ProtoReflect.Descriptor instead.
func (*SendForgotPasswordEmailRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{14}
}

func (x *SendForgotPasswordEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type SendForgotPasswordEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendForgotPasswordEmailResponse) Reset() {
	*x = SendForgotPasswordEmailResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendForgotPasswordEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendForgotPasswordEmailResponse) ProtoMessage() {}

func (x *SendForgotPasswordEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendForgotPasswordEmailResponse.ProtoReflect.Descriptor instead.
func (*SendForgotPasswordEmailResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{15}
}

func (x *SendForgotPasswordEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SendForgotPasswordEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ResetPasswordRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Email            string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	VerificationCode string                 `protobuf:"bytes,2,opt,name=verification_code,json=verificationCode,proto3" json:"verification_code,omitempty"`
	NewPassword      string                 `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ResetPasswordRequest) Reset() {
	*x = ResetPasswordRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordRequest) ProtoMessage() {}

func (x *ResetPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordRequest.ProtoReflect.Descriptor instead.
func (*ResetPasswordRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{16}
}

func (x *ResetPasswordRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ResetPasswordRequest) GetVerificationCode() string {
	if x != nil {
		return x.VerificationCode
	}
	return ""
}

func (x *ResetPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type ResetPasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetPasswordResponse) Reset() {
	*x = ResetPasswordResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetPasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordResponse) ProtoMessage() {}

func (x *ResetPasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordResponse.ProtoReflect.Descriptor instead.
func (*ResetPasswordResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{17}
}

func (x *ResetPasswordResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ResetPasswordResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ChangePasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OldPassword   string                 `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword   string                 `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangePasswordRequest) Reset() {
	*x = ChangePasswordRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordRequest) ProtoMessage() {}

func (x *ChangePasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordRequest.ProtoReflect.Descriptor instead.
func (*ChangePasswordRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{18}
}

func (x *ChangePasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *ChangePasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type ChangePasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangePasswordResponse) Reset() {
	*x = ChangePasswordResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordResponse) ProtoMessage() {}

func (x *ChangePasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordResponse.ProtoReflect.Descriptor instead.
func (*ChangePasswordResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{19}
}

func (x *ChangePasswordResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ChangePasswordResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ChangeEmailVerificationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeEmailVerificationRequest) Reset() {
	*x = ChangeEmailVerificationRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeEmailVerificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeEmailVerificationRequest) ProtoMessage() {}

func (x *ChangeEmailVerificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeEmailVerificationRequest.ProtoReflect.Descriptor instead.
func (*ChangeEmailVerificationRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{20}
}

type ChangeEmailVerificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeEmailVerificationResponse) Reset() {
	*x = ChangeEmailVerificationResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeEmailVerificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeEmailVerificationResponse) ProtoMessage() {}

func (x *ChangeEmailVerificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeEmailVerificationResponse.ProtoReflect.Descriptor instead.
func (*ChangeEmailVerificationResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{21}
}

func (x *ChangeEmailVerificationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ChangeEmailVerificationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type VerifyChangeEmailRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	VerificationCode string                 `protobuf:"bytes,1,opt,name=verification_code,json=verificationCode,proto3" json:"verification_code,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *VerifyChangeEmailRequest) Reset() {
	*x = VerifyChangeEmailRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyChangeEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyChangeEmailRequest) ProtoMessage() {}

func (x *VerifyChangeEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyChangeEmailRequest.ProtoReflect.Descriptor instead.
func (*VerifyChangeEmailRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{22}
}

func (x *VerifyChangeEmailRequest) GetVerificationCode() string {
	if x != nil {
		return x.VerificationCode
	}
	return ""
}

type VerifyChangeEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyChangeEmailResponse) Reset() {
	*x = VerifyChangeEmailResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyChangeEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyChangeEmailResponse) ProtoMessage() {}

func (x *VerifyChangeEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyChangeEmailResponse.ProtoReflect.Descriptor instead.
func (*VerifyChangeEmailResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{23}
}

func (x *VerifyChangeEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *VerifyChangeEmailResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type SendVerifyNewEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NewEmail      string                 `protobuf:"bytes,2,opt,name=new_email,json=newEmail,proto3" json:"new_email,omitempty"`
	Token         string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyNewEmailRequest) Reset() {
	*x = SendVerifyNewEmailRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyNewEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyNewEmailRequest) ProtoMessage() {}

func (x *SendVerifyNewEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyNewEmailRequest.ProtoReflect.Descriptor instead.
func (*SendVerifyNewEmailRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{24}
}

func (x *SendVerifyNewEmailRequest) GetNewEmail() string {
	if x != nil {
		return x.NewEmail
	}
	return ""
}

func (x *SendVerifyNewEmailRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type SendVerifyNewEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyNewEmailResponse) Reset() {
	*x = SendVerifyNewEmailResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyNewEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyNewEmailResponse) ProtoMessage() {}

func (x *SendVerifyNewEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyNewEmailResponse.ProtoReflect.Descriptor instead.
func (*SendVerifyNewEmailResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{25}
}

func (x *SendVerifyNewEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SendVerifyNewEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type VerifyNewEmailWithCodeRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	VerificationCode string                 `protobuf:"bytes,1,opt,name=verification_code,json=verificationCode,proto3" json:"verification_code,omitempty"`
	NewEmail         string                 `protobuf:"bytes,2,opt,name=new_email,json=newEmail,proto3" json:"new_email,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *VerifyNewEmailWithCodeRequest) Reset() {
	*x = VerifyNewEmailWithCodeRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyNewEmailWithCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyNewEmailWithCodeRequest) ProtoMessage() {}

func (x *VerifyNewEmailWithCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyNewEmailWithCodeRequest.ProtoReflect.Descriptor instead.
func (*VerifyNewEmailWithCodeRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{26}
}

func (x *VerifyNewEmailWithCodeRequest) GetVerificationCode() string {
	if x != nil {
		return x.VerificationCode
	}
	return ""
}

func (x *VerifyNewEmailWithCodeRequest) GetNewEmail() string {
	if x != nil {
		return x.NewEmail
	}
	return ""
}

type VerifyNewEmailRequest struct {
	state                  protoimpl.MessageState         `protogen:"open.v1"`
	VerifyNewEmailWithCode *VerifyNewEmailWithCodeRequest `protobuf:"bytes,1,opt,name=verify_new_email_with_code,json=verifyNewEmailWithCode,proto3,oneof" json:"verify_new_email_with_code,omitempty"`
	Token                  *string                        `protobuf:"bytes,2,opt,name=token,proto3,oneof" json:"token,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *VerifyNewEmailRequest) Reset() {
	*x = VerifyNewEmailRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyNewEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyNewEmailRequest) ProtoMessage() {}

func (x *VerifyNewEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyNewEmailRequest.ProtoReflect.Descriptor instead.
func (*VerifyNewEmailRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{27}
}

func (x *VerifyNewEmailRequest) GetVerifyNewEmailWithCode() *VerifyNewEmailWithCodeRequest {
	if x != nil {
		return x.VerifyNewEmailWithCode
	}
	return nil
}

func (x *VerifyNewEmailRequest) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

type VerifyNewEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyNewEmailResponse) Reset() {
	*x = VerifyNewEmailResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyNewEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyNewEmailResponse) ProtoMessage() {}

func (x *VerifyNewEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyNewEmailResponse.ProtoReflect.Descriptor instead.
func (*VerifyNewEmailResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{28}
}

func (x *VerifyNewEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *VerifyNewEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateUserDetailsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FullName      string                 `protobuf:"bytes,1,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	DateOfBirth   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date_of_birth,json=dateOfBirth,proto3,oneof" json:"date_of_birth,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserDetailsRequest) Reset() {
	*x = UpdateUserDetailsRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDetailsRequest) ProtoMessage() {}

func (x *UpdateUserDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserDetailsRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateUserDetailsRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UpdateUserDetailsRequest) GetDateOfBirth() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

type UpdateUserDetailsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserDetailsResponse) Reset() {
	*x = UpdateUserDetailsResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDetailsResponse) ProtoMessage() {}

func (x *UpdateUserDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserDetailsResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateUserDetailsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateUserDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateUserImageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Image         string                 `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserImageRequest) Reset() {
	*x = UpdateUserImageRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserImageRequest) ProtoMessage() {}

func (x *UpdateUserImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserImageRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserImageRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{31}
}

func (x *UpdateUserImageRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type UpdateUserImageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserImageResponse) Reset() {
	*x = UpdateUserImageResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserImageResponse) ProtoMessage() {}

func (x *UpdateUserImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserImageResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserImageResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{32}
}

func (x *UpdateUserImageResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateUserImageResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ListSessionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSessionsRequest) Reset() {
	*x = ListSessionsRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSessionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSessionsRequest) ProtoMessage() {}

func (x *ListSessionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSessionsRequest.ProtoReflect.Descriptor instead.
func (*ListSessionsRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{33}
}

type Session struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserAgent     string                 `protobuf:"bytes,2,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	IpAddress     string                 `protobuf:"bytes,3,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsCurrent     bool                   `protobuf:"varint,5,opt,name=isCurrent,proto3" json:"isCurrent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Session) Reset() {
	*x = Session{}
	mi := &file_users_v1_auth_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Session) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Session) ProtoMessage() {}

func (x *Session) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Session.ProtoReflect.Descriptor instead.
func (*Session) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{34}
}

func (x *Session) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Session) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *Session) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *Session) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Session) GetIsCurrent() bool {
	if x != nil {
		return x.IsCurrent
	}
	return false
}

type ListSessionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sessions      []*Session             `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSessionsResponse) Reset() {
	*x = ListSessionsResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSessionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSessionsResponse) ProtoMessage() {}

func (x *ListSessionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSessionsResponse.ProtoReflect.Descriptor instead.
func (*ListSessionsResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{35}
}

func (x *ListSessionsResponse) GetSessions() []*Session {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type RevokeSessionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionIds    []string               `protobuf:"bytes,1,rep,name=session_ids,json=sessionIds,proto3" json:"session_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeSessionsRequest) Reset() {
	*x = RevokeSessionsRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeSessionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeSessionsRequest) ProtoMessage() {}

func (x *RevokeSessionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeSessionsRequest.ProtoReflect.Descriptor instead.
func (*RevokeSessionsRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{36}
}

func (x *RevokeSessionsRequest) GetSessionIds() []string {
	if x != nil {
		return x.SessionIds
	}
	return nil
}

type RevokeSessionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeSessionsResponse) Reset() {
	*x = RevokeSessionsResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeSessionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeSessionsResponse) ProtoMessage() {}

func (x *RevokeSessionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeSessionsResponse.ProtoReflect.Descriptor instead.
func (*RevokeSessionsResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{37}
}

func (x *RevokeSessionsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RevokeSessionsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RevokeOtherSessionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeOtherSessionsRequest) Reset() {
	*x = RevokeOtherSessionsRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeOtherSessionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeOtherSessionsRequest) ProtoMessage() {}

func (x *RevokeOtherSessionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeOtherSessionsRequest.ProtoReflect.Descriptor instead.
func (*RevokeOtherSessionsRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{38}
}

type RevokeOtherSessionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeOtherSessionsResponse) Reset() {
	*x = RevokeOtherSessionsResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeOtherSessionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeOtherSessionsResponse) ProtoMessage() {}

func (x *RevokeOtherSessionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeOtherSessionsResponse.ProtoReflect.Descriptor instead.
func (*RevokeOtherSessionsResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{39}
}

func (x *RevokeOtherSessionsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RevokeOtherSessionsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type LinkSocialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Provider      string                 `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkSocialRequest) Reset() {
	*x = LinkSocialRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkSocialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkSocialRequest) ProtoMessage() {}

func (x *LinkSocialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkSocialRequest.ProtoReflect.Descriptor instead.
func (*LinkSocialRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{40}
}

func (x *LinkSocialRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *LinkSocialRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type LinkSocialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkSocialResponse) Reset() {
	*x = LinkSocialResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkSocialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkSocialResponse) ProtoMessage() {}

func (x *LinkSocialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkSocialResponse.ProtoReflect.Descriptor instead.
func (*LinkSocialResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{41}
}

func (x *LinkSocialResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *LinkSocialResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ListAccountsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAccountsRequest) Reset() {
	*x = ListAccountsRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountsRequest) ProtoMessage() {}

func (x *ListAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountsRequest.ProtoReflect.Descriptor instead.
func (*ListAccountsRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{42}
}

type ListAccountsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accounts      []*Account             `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAccountsResponse) Reset() {
	*x = ListAccountsResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountsResponse) ProtoMessage() {}

func (x *ListAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountsResponse.ProtoReflect.Descriptor instead.
func (*ListAccountsResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{43}
}

func (x *ListAccountsResponse) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type Account struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Provider      string                 `protobuf:"bytes,2,opt,name=provider,proto3" json:"provider,omitempty"`
	UserId        string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Account) Reset() {
	*x = Account{}
	mi := &file_users_v1_auth_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{44}
}

func (x *Account) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Account) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *Account) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UnlinkAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     string                 `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnlinkAccountRequest) Reset() {
	*x = UnlinkAccountRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnlinkAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkAccountRequest) ProtoMessage() {}

func (x *UnlinkAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkAccountRequest.ProtoReflect.Descriptor instead.
func (*UnlinkAccountRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{45}
}

func (x *UnlinkAccountRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type UnlinkAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnlinkAccountResponse) Reset() {
	*x = UnlinkAccountResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnlinkAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkAccountResponse) ProtoMessage() {}

func (x *UnlinkAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkAccountResponse.ProtoReflect.Descriptor instead.
func (*UnlinkAccountResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{46}
}

func (x *UnlinkAccountResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UnlinkAccountResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RefreshTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	mi := &file_users_v1_auth_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{47}
}

func (x *RefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type RefreshTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenResponse) Reset() {
	*x = RefreshTokenResponse{}
	mi := &file_users_v1_auth_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResponse) ProtoMessage() {}

func (x *RefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_auth_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*RefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_auth_proto_rawDescGZIP(), []int{48}
}

func (x *RefreshTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *RefreshTokenResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

var File_users_v1_auth_proto protoreflect.FileDescriptor

const file_users_v1_auth_proto_rawDesc = "" +
	"\n" +
	"\x13users/v1/auth.proto\x12\fapi.users.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x14users/v1/users.proto\x1a\x1fvtubers/v1/vtuberprofiles.proto\"I\n" +
	"\x17SignInWithSocialRequest\x12\x1a\n" +
	"\bprovider\x18\x01 \x01(\tR\bprovider\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\"b\n" +
	"\x18SignInWithSocialResponse\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x02 \x01(\tR\frefreshToken\"\x13\n" +
	"\x11GetSessionRequest\"s\n" +
	"\x12GetSessionResponse\x12&\n" +
	"\x04user\x18\x01 \x01(\v2\x12.api.users.v1.UserR\x04user\x125\n" +
	"\x06vtuber\x18\x02 \x01(\v2\x1d.api.vtubers.v1.VtuberProfileR\x06vtuber\"\x10\n" +
	"\x0eSignOutRequest\"E\n" +
	"\x0fSignOutResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\">\n" +
	"&SendSignupEmailVerificationCodeRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\"]\n" +
	"'SendSignupEmailVerificationCodeResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"]\n" +
	"\x18VerifySignupEmailRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12+\n" +
	"\x11verification_code\x18\x02 \x01(\tR\x10verificationCode\"1\n" +
	"\x19VerifySignupEmailResponse\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\xbe\x01\n" +
	"\x16SignupWithEmailRequest\x12\x1b\n" +
	"\tfull_name\x18\x01 \x01(\tR\bfullName\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12C\n" +
	"\rdate_of_birth\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\vdateOfBirth\x88\x01\x01\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05tokenB\x10\n" +
	"\x0e_date_of_birth\"M\n" +
	"\x17SignupWithEmailResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"J\n" +
	"\x16SignInWithEmailRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"a\n" +
	"\x17SignInWithEmailResponse\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x02 \x01(\tR\frefreshToken\"6\n" +
	"\x1eSendForgotPasswordEmailRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\"U\n" +
	"\x1fSendForgotPasswordEmailResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"|\n" +
	"\x14ResetPasswordRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12+\n" +
	"\x11verification_code\x18\x02 \x01(\tR\x10verificationCode\x12!\n" +
	"\fnew_password\x18\x03 \x01(\tR\vnewPassword\"K\n" +
	"\x15ResetPasswordResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"]\n" +
	"\x15ChangePasswordRequest\x12!\n" +
	"\fold_password\x18\x01 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x02 \x01(\tR\vnewPassword\"L\n" +
	"\x16ChangePasswordResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\" \n" +
	"\x1eChangeEmailVerificationRequest\"U\n" +
	"\x1fChangeEmailVerificationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"G\n" +
	"\x18VerifyChangeEmailRequest\x12+\n" +
	"\x11verification_code\x18\x01 \x01(\tR\x10verificationCode\"K\n" +
	"\x19VerifyChangeEmailResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\"N\n" +
	"\x19SendVerifyNewEmailRequest\x12\x1b\n" +
	"\tnew_email\x18\x02 \x01(\tR\bnewEmail\x12\x14\n" +
	"\x05token\x18\x03 \x01(\tR\x05token\"P\n" +
	"\x1aSendVerifyNewEmailResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"i\n" +
	"\x1dVerifyNewEmailWithCodeRequest\x12+\n" +
	"\x11verification_code\x18\x01 \x01(\tR\x10verificationCode\x12\x1b\n" +
	"\tnew_email\x18\x02 \x01(\tR\bnewEmail\"\xc9\x01\n" +
	"\x15VerifyNewEmailRequest\x12l\n" +
	"\x1averify_new_email_with_code\x18\x01 \x01(\v2+.api.users.v1.VerifyNewEmailWithCodeRequestH\x00R\x16verifyNewEmailWithCode\x88\x01\x01\x12\x19\n" +
	"\x05token\x18\x02 \x01(\tH\x01R\x05token\x88\x01\x01B\x1d\n" +
	"\x1b_verify_new_email_with_codeB\b\n" +
	"\x06_token\"L\n" +
	"\x16VerifyNewEmailResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x8e\x01\n" +
	"\x18UpdateUserDetailsRequest\x12\x1b\n" +
	"\tfull_name\x18\x01 \x01(\tR\bfullName\x12C\n" +
	"\rdate_of_birth\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\vdateOfBirth\x88\x01\x01B\x10\n" +
	"\x0e_date_of_birth\"O\n" +
	"\x19UpdateUserDetailsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\".\n" +
	"\x16UpdateUserImageRequest\x12\x14\n" +
	"\x05image\x18\x01 \x01(\tR\x05image\"M\n" +
	"\x17UpdateUserImageResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x15\n" +
	"\x13ListSessionsRequest\"\x94\x01\n" +
	"\aSession\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"user_agent\x18\x02 \x01(\tR\tuserAgent\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x03 \x01(\tR\tipAddress\x12\x1d\n" +
	"\n" +
	"created_at\x18\x04 \x01(\tR\tcreatedAt\x12\x1c\n" +
	"\tisCurrent\x18\x05 \x01(\bR\tisCurrent\"I\n" +
	"\x14ListSessionsResponse\x121\n" +
	"\bsessions\x18\x01 \x03(\v2\x15.api.users.v1.SessionR\bsessions\"8\n" +
	"\x15RevokeSessionsRequest\x12\x1f\n" +
	"\vsession_ids\x18\x01 \x03(\tR\n" +
	"sessionIds\"L\n" +
	"\x16RevokeSessionsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x1c\n" +
	"\x1aRevokeOtherSessionsRequest\"Q\n" +
	"\x1bRevokeOtherSessionsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"C\n" +
	"\x11LinkSocialRequest\x12\x1a\n" +
	"\bprovider\x18\x01 \x01(\tR\bprovider\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\"H\n" +
	"\x12LinkSocialResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x15\n" +
	"\x13ListAccountsRequest\"I\n" +
	"\x14ListAccountsResponse\x121\n" +
	"\baccounts\x18\x01 \x03(\v2\x15.api.users.v1.AccountR\baccounts\"N\n" +
	"\aAccount\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bprovider\x18\x02 \x01(\tR\bprovider\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\"5\n" +
	"\x14UnlinkAccountRequest\x12\x1d\n" +
	"\n" +
	"account_id\x18\x01 \x01(\tR\taccountId\"K\n" +
	"\x15UnlinkAccountResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\":\n" +
	"\x13RefreshTokenRequest\x12#\n" +
	"\rrefresh_token\x18\x01 \x01(\tR\frefreshToken\"^\n" +
	"\x14RefreshTokenResponse\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x02 \x01(\tR\frefreshToken2\xda\x12\n" +
	"\vAuthService\x12`\n" +
	"\x0fSignInWithEmail\x12$.api.users.v1.SignInWithEmailRequest\x1a%.api.users.v1.SignInWithEmailResponse\"\x00\x12W\n" +
	"\fRefreshToken\x12!.api.users.v1.RefreshTokenRequest\x1a\".api.users.v1.RefreshTokenResponse\"\x00\x12c\n" +
	"\x10SignInWithSocial\x12%.api.users.v1.SignInWithSocialRequest\x1a&.api.users.v1.SignInWithSocialResponse\"\x00\x12N\n" +
	"\aSignOut\x12\x1c.api.users.v1.SignOutRequest\x1a\x1d.api.users.v1.SignOutResponse\"\x06\x82\xb5\x18\x02\b\x01\x12W\n" +
	"\n" +
	"GetSession\x12\x1f.api.users.v1.GetSessionRequest\x1a .api.users.v1.GetSessionResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x90\x01\n" +
	"\x1fSendSignupEmailVerificationCode\x124.api.users.v1.SendSignupEmailVerificationCodeRequest\x1a5.api.users.v1.SendSignupEmailVerificationCodeResponse\"\x00\x12f\n" +
	"\x11VerifySignupEmail\x12&.api.users.v1.VerifySignupEmailRequest\x1a'.api.users.v1.VerifySignupEmailResponse\"\x00\x12`\n" +
	"\x0fSignupWithEmail\x12$.api.users.v1.SignupWithEmailRequest\x1a%.api.users.v1.SignupWithEmailResponse\"\x00\x12x\n" +
	"\x17SendForgotPasswordEmail\x12,.api.users.v1.SendForgotPasswordEmailRequest\x1a-.api.users.v1.SendForgotPasswordEmailResponse\"\x00\x12Z\n" +
	"\rResetPassword\x12\".api.users.v1.ResetPasswordRequest\x1a#.api.users.v1.ResetPasswordResponse\"\x00\x12c\n" +
	"\x0eChangePassword\x12#.api.users.v1.ChangePasswordRequest\x1a$.api.users.v1.ChangePasswordResponse\"\x06\x82\xb5\x18\x02\b\x01\x12~\n" +
	"\x17ChangeEmailVerification\x12,.api.users.v1.ChangeEmailVerificationRequest\x1a-.api.users.v1.ChangeEmailVerificationResponse\"\x06\x82\xb5\x18\x02\b\x01\x12l\n" +
	"\x11VerifyChangeEmail\x12&.api.users.v1.VerifyChangeEmailRequest\x1a'.api.users.v1.VerifyChangeEmailResponse\"\x06\x82\xb5\x18\x02\b\x01\x12o\n" +
	"\x12SendVerifyNewEmail\x12'.api.users.v1.SendVerifyNewEmailRequest\x1a(.api.users.v1.SendVerifyNewEmailResponse\"\x06\x82\xb5\x18\x02\b\x01\x12c\n" +
	"\x0eVerifyNewEmail\x12#.api.users.v1.VerifyNewEmailRequest\x1a$.api.users.v1.VerifyNewEmailResponse\"\x06\x82\xb5\x18\x02\b\x01\x12l\n" +
	"\x11UpdateUserDetails\x12&.api.users.v1.UpdateUserDetailsRequest\x1a'.api.users.v1.UpdateUserDetailsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12f\n" +
	"\x0fUpdateUserImage\x12$.api.users.v1.UpdateUserImageRequest\x1a%.api.users.v1.UpdateUserImageResponse\"\x06\x82\xb5\x18\x02\b\x01\x12]\n" +
	"\fListSessions\x12!.api.users.v1.ListSessionsRequest\x1a\".api.users.v1.ListSessionsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12c\n" +
	"\x0eRevokeSessions\x12#.api.users.v1.RevokeSessionsRequest\x1a$.api.users.v1.RevokeSessionsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12r\n" +
	"\x13RevokeOtherSessions\x12(.api.users.v1.RevokeOtherSessionsRequest\x1a).api.users.v1.RevokeOtherSessionsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12W\n" +
	"\n" +
	"LinkSocial\x12\x1f.api.users.v1.LinkSocialRequest\x1a .api.users.v1.LinkSocialResponse\"\x06\x82\xb5\x18\x02\b\x01\x12]\n" +
	"\fListAccounts\x12!.api.users.v1.ListAccountsRequest\x1a\".api.users.v1.ListAccountsResponse\"\x06\x82\xb5\x18\x02\b\x01\x12`\n" +
	"\rUnlinkAccount\x12\".api.users.v1.UnlinkAccountRequest\x1a#.api.users.v1.UnlinkAccountResponse\"\x06\x82\xb5\x18\x02\b\x01B0Z.github.com/nsp-inc/vtuber/api/users/v1;usersv1b\x06proto3"

var (
	file_users_v1_auth_proto_rawDescOnce sync.Once
	file_users_v1_auth_proto_rawDescData []byte
)

func file_users_v1_auth_proto_rawDescGZIP() []byte {
	file_users_v1_auth_proto_rawDescOnce.Do(func() {
		file_users_v1_auth_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_users_v1_auth_proto_rawDesc), len(file_users_v1_auth_proto_rawDesc)))
	})
	return file_users_v1_auth_proto_rawDescData
}

var file_users_v1_auth_proto_msgTypes = make([]protoimpl.MessageInfo, 49)
var file_users_v1_auth_proto_goTypes = []any{
	(*SignInWithSocialRequest)(nil),                 // 0: api.users.v1.SignInWithSocialRequest
	(*SignInWithSocialResponse)(nil),                // 1: api.users.v1.SignInWithSocialResponse
	(*GetSessionRequest)(nil),                       // 2: api.users.v1.GetSessionRequest
	(*GetSessionResponse)(nil),                      // 3: api.users.v1.GetSessionResponse
	(*SignOutRequest)(nil),                          // 4: api.users.v1.SignOutRequest
	(*SignOutResponse)(nil),                         // 5: api.users.v1.SignOutResponse
	(*SendSignupEmailVerificationCodeRequest)(nil),  // 6: api.users.v1.SendSignupEmailVerificationCodeRequest
	(*SendSignupEmailVerificationCodeResponse)(nil), // 7: api.users.v1.SendSignupEmailVerificationCodeResponse
	(*VerifySignupEmailRequest)(nil),                // 8: api.users.v1.VerifySignupEmailRequest
	(*VerifySignupEmailResponse)(nil),               // 9: api.users.v1.VerifySignupEmailResponse
	(*SignupWithEmailRequest)(nil),                  // 10: api.users.v1.SignupWithEmailRequest
	(*SignupWithEmailResponse)(nil),                 // 11: api.users.v1.SignupWithEmailResponse
	(*SignInWithEmailRequest)(nil),                  // 12: api.users.v1.SignInWithEmailRequest
	(*SignInWithEmailResponse)(nil),                 // 13: api.users.v1.SignInWithEmailResponse
	(*SendForgotPasswordEmailRequest)(nil),          // 14: api.users.v1.SendForgotPasswordEmailRequest
	(*SendForgotPasswordEmailResponse)(nil),         // 15: api.users.v1.SendForgotPasswordEmailResponse
	(*ResetPasswordRequest)(nil),                    // 16: api.users.v1.ResetPasswordRequest
	(*ResetPasswordResponse)(nil),                   // 17: api.users.v1.ResetPasswordResponse
	(*ChangePasswordRequest)(nil),                   // 18: api.users.v1.ChangePasswordRequest
	(*ChangePasswordResponse)(nil),                  // 19: api.users.v1.ChangePasswordResponse
	(*ChangeEmailVerificationRequest)(nil),          // 20: api.users.v1.ChangeEmailVerificationRequest
	(*ChangeEmailVerificationResponse)(nil),         // 21: api.users.v1.ChangeEmailVerificationResponse
	(*VerifyChangeEmailRequest)(nil),                // 22: api.users.v1.VerifyChangeEmailRequest
	(*VerifyChangeEmailResponse)(nil),               // 23: api.users.v1.VerifyChangeEmailResponse
	(*SendVerifyNewEmailRequest)(nil),               // 24: api.users.v1.SendVerifyNewEmailRequest
	(*SendVerifyNewEmailResponse)(nil),              // 25: api.users.v1.SendVerifyNewEmailResponse
	(*VerifyNewEmailWithCodeRequest)(nil),           // 26: api.users.v1.VerifyNewEmailWithCodeRequest
	(*VerifyNewEmailRequest)(nil),                   // 27: api.users.v1.VerifyNewEmailRequest
	(*VerifyNewEmailResponse)(nil),                  // 28: api.users.v1.VerifyNewEmailResponse
	(*UpdateUserDetailsRequest)(nil),                // 29: api.users.v1.UpdateUserDetailsRequest
	(*UpdateUserDetailsResponse)(nil),               // 30: api.users.v1.UpdateUserDetailsResponse
	(*UpdateUserImageRequest)(nil),                  // 31: api.users.v1.UpdateUserImageRequest
	(*UpdateUserImageResponse)(nil),                 // 32: api.users.v1.UpdateUserImageResponse
	(*ListSessionsRequest)(nil),                     // 33: api.users.v1.ListSessionsRequest
	(*Session)(nil),                                 // 34: api.users.v1.Session
	(*ListSessionsResponse)(nil),                    // 35: api.users.v1.ListSessionsResponse
	(*RevokeSessionsRequest)(nil),                   // 36: api.users.v1.RevokeSessionsRequest
	(*RevokeSessionsResponse)(nil),                  // 37: api.users.v1.RevokeSessionsResponse
	(*RevokeOtherSessionsRequest)(nil),              // 38: api.users.v1.RevokeOtherSessionsRequest
	(*RevokeOtherSessionsResponse)(nil),             // 39: api.users.v1.RevokeOtherSessionsResponse
	(*LinkSocialRequest)(nil),                       // 40: api.users.v1.LinkSocialRequest
	(*LinkSocialResponse)(nil),                      // 41: api.users.v1.LinkSocialResponse
	(*ListAccountsRequest)(nil),                     // 42: api.users.v1.ListAccountsRequest
	(*ListAccountsResponse)(nil),                    // 43: api.users.v1.ListAccountsResponse
	(*Account)(nil),                                 // 44: api.users.v1.Account
	(*UnlinkAccountRequest)(nil),                    // 45: api.users.v1.UnlinkAccountRequest
	(*UnlinkAccountResponse)(nil),                   // 46: api.users.v1.UnlinkAccountResponse
	(*RefreshTokenRequest)(nil),                     // 47: api.users.v1.RefreshTokenRequest
	(*RefreshTokenResponse)(nil),                    // 48: api.users.v1.RefreshTokenResponse
	(*User)(nil),                                    // 49: api.users.v1.User
	(*v1.VtuberProfile)(nil),                        // 50: api.vtubers.v1.VtuberProfile
	(*timestamppb.Timestamp)(nil),                   // 51: google.protobuf.Timestamp
}
var file_users_v1_auth_proto_depIdxs = []int32{
	49, // 0: api.users.v1.GetSessionResponse.user:type_name -> api.users.v1.User
	50, // 1: api.users.v1.GetSessionResponse.vtuber:type_name -> api.vtubers.v1.VtuberProfile
	51, // 2: api.users.v1.SignupWithEmailRequest.date_of_birth:type_name -> google.protobuf.Timestamp
	26, // 3: api.users.v1.VerifyNewEmailRequest.verify_new_email_with_code:type_name -> api.users.v1.VerifyNewEmailWithCodeRequest
	51, // 4: api.users.v1.UpdateUserDetailsRequest.date_of_birth:type_name -> google.protobuf.Timestamp
	34, // 5: api.users.v1.ListSessionsResponse.sessions:type_name -> api.users.v1.Session
	44, // 6: api.users.v1.ListAccountsResponse.accounts:type_name -> api.users.v1.Account
	12, // 7: api.users.v1.AuthService.SignInWithEmail:input_type -> api.users.v1.SignInWithEmailRequest
	47, // 8: api.users.v1.AuthService.RefreshToken:input_type -> api.users.v1.RefreshTokenRequest
	0,  // 9: api.users.v1.AuthService.SignInWithSocial:input_type -> api.users.v1.SignInWithSocialRequest
	4,  // 10: api.users.v1.AuthService.SignOut:input_type -> api.users.v1.SignOutRequest
	2,  // 11: api.users.v1.AuthService.GetSession:input_type -> api.users.v1.GetSessionRequest
	6,  // 12: api.users.v1.AuthService.SendSignupEmailVerificationCode:input_type -> api.users.v1.SendSignupEmailVerificationCodeRequest
	8,  // 13: api.users.v1.AuthService.VerifySignupEmail:input_type -> api.users.v1.VerifySignupEmailRequest
	10, // 14: api.users.v1.AuthService.SignupWithEmail:input_type -> api.users.v1.SignupWithEmailRequest
	14, // 15: api.users.v1.AuthService.SendForgotPasswordEmail:input_type -> api.users.v1.SendForgotPasswordEmailRequest
	16, // 16: api.users.v1.AuthService.ResetPassword:input_type -> api.users.v1.ResetPasswordRequest
	18, // 17: api.users.v1.AuthService.ChangePassword:input_type -> api.users.v1.ChangePasswordRequest
	20, // 18: api.users.v1.AuthService.ChangeEmailVerification:input_type -> api.users.v1.ChangeEmailVerificationRequest
	22, // 19: api.users.v1.AuthService.VerifyChangeEmail:input_type -> api.users.v1.VerifyChangeEmailRequest
	24, // 20: api.users.v1.AuthService.SendVerifyNewEmail:input_type -> api.users.v1.SendVerifyNewEmailRequest
	27, // 21: api.users.v1.AuthService.VerifyNewEmail:input_type -> api.users.v1.VerifyNewEmailRequest
	29, // 22: api.users.v1.AuthService.UpdateUserDetails:input_type -> api.users.v1.UpdateUserDetailsRequest
	31, // 23: api.users.v1.AuthService.UpdateUserImage:input_type -> api.users.v1.UpdateUserImageRequest
	33, // 24: api.users.v1.AuthService.ListSessions:input_type -> api.users.v1.ListSessionsRequest
	36, // 25: api.users.v1.AuthService.RevokeSessions:input_type -> api.users.v1.RevokeSessionsRequest
	38, // 26: api.users.v1.AuthService.RevokeOtherSessions:input_type -> api.users.v1.RevokeOtherSessionsRequest
	40, // 27: api.users.v1.AuthService.LinkSocial:input_type -> api.users.v1.LinkSocialRequest
	42, // 28: api.users.v1.AuthService.ListAccounts:input_type -> api.users.v1.ListAccountsRequest
	45, // 29: api.users.v1.AuthService.UnlinkAccount:input_type -> api.users.v1.UnlinkAccountRequest
	13, // 30: api.users.v1.AuthService.SignInWithEmail:output_type -> api.users.v1.SignInWithEmailResponse
	48, // 31: api.users.v1.AuthService.RefreshToken:output_type -> api.users.v1.RefreshTokenResponse
	1,  // 32: api.users.v1.AuthService.SignInWithSocial:output_type -> api.users.v1.SignInWithSocialResponse
	5,  // 33: api.users.v1.AuthService.SignOut:output_type -> api.users.v1.SignOutResponse
	3,  // 34: api.users.v1.AuthService.GetSession:output_type -> api.users.v1.GetSessionResponse
	7,  // 35: api.users.v1.AuthService.SendSignupEmailVerificationCode:output_type -> api.users.v1.SendSignupEmailVerificationCodeResponse
	9,  // 36: api.users.v1.AuthService.VerifySignupEmail:output_type -> api.users.v1.VerifySignupEmailResponse
	11, // 37: api.users.v1.AuthService.SignupWithEmail:output_type -> api.users.v1.SignupWithEmailResponse
	15, // 38: api.users.v1.AuthService.SendForgotPasswordEmail:output_type -> api.users.v1.SendForgotPasswordEmailResponse
	17, // 39: api.users.v1.AuthService.ResetPassword:output_type -> api.users.v1.ResetPasswordResponse
	19, // 40: api.users.v1.AuthService.ChangePassword:output_type -> api.users.v1.ChangePasswordResponse
	21, // 41: api.users.v1.AuthService.ChangeEmailVerification:output_type -> api.users.v1.ChangeEmailVerificationResponse
	23, // 42: api.users.v1.AuthService.VerifyChangeEmail:output_type -> api.users.v1.VerifyChangeEmailResponse
	25, // 43: api.users.v1.AuthService.SendVerifyNewEmail:output_type -> api.users.v1.SendVerifyNewEmailResponse
	28, // 44: api.users.v1.AuthService.VerifyNewEmail:output_type -> api.users.v1.VerifyNewEmailResponse
	30, // 45: api.users.v1.AuthService.UpdateUserDetails:output_type -> api.users.v1.UpdateUserDetailsResponse
	32, // 46: api.users.v1.AuthService.UpdateUserImage:output_type -> api.users.v1.UpdateUserImageResponse
	35, // 47: api.users.v1.AuthService.ListSessions:output_type -> api.users.v1.ListSessionsResponse
	37, // 48: api.users.v1.AuthService.RevokeSessions:output_type -> api.users.v1.RevokeSessionsResponse
	39, // 49: api.users.v1.AuthService.RevokeOtherSessions:output_type -> api.users.v1.RevokeOtherSessionsResponse
	41, // 50: api.users.v1.AuthService.LinkSocial:output_type -> api.users.v1.LinkSocialResponse
	43, // 51: api.users.v1.AuthService.ListAccounts:output_type -> api.users.v1.ListAccountsResponse
	46, // 52: api.users.v1.AuthService.UnlinkAccount:output_type -> api.users.v1.UnlinkAccountResponse
	30, // [30:53] is the sub-list for method output_type
	7,  // [7:30] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_users_v1_auth_proto_init() }
func file_users_v1_auth_proto_init() {
	if File_users_v1_auth_proto != nil {
		return
	}
	file_users_v1_users_proto_init()
	file_users_v1_auth_proto_msgTypes[10].OneofWrappers = []any{}
	file_users_v1_auth_proto_msgTypes[27].OneofWrappers = []any{}
	file_users_v1_auth_proto_msgTypes[29].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_users_v1_auth_proto_rawDesc), len(file_users_v1_auth_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   49,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_users_v1_auth_proto_goTypes,
		DependencyIndexes: file_users_v1_auth_proto_depIdxs,
		MessageInfos:      file_users_v1_auth_proto_msgTypes,
	}.Build()
	File_users_v1_auth_proto = out.File
	file_users_v1_auth_proto_goTypes = nil
	file_users_v1_auth_proto_depIdxs = nil
}
