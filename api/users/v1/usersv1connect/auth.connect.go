// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: users/v1/auth.proto

package usersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/users/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// AuthServiceName is the fully-qualified name of the AuthService service.
	AuthServiceName = "api.users.v1.AuthService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// AuthServiceSignInWithEmailProcedure is the fully-qualified name of the AuthService's
	// SignInWithEmail RPC.
	AuthServiceSignInWithEmailProcedure = "/api.users.v1.AuthService/SignInWithEmail"
	// AuthServiceRefreshTokenProcedure is the fully-qualified name of the AuthService's RefreshToken
	// RPC.
	AuthServiceRefreshTokenProcedure = "/api.users.v1.AuthService/RefreshToken"
	// AuthServiceSignInWithSocialProcedure is the fully-qualified name of the AuthService's
	// SignInWithSocial RPC.
	AuthServiceSignInWithSocialProcedure = "/api.users.v1.AuthService/SignInWithSocial"
	// AuthServiceSignOutProcedure is the fully-qualified name of the AuthService's SignOut RPC.
	AuthServiceSignOutProcedure = "/api.users.v1.AuthService/SignOut"
	// AuthServiceGetSessionProcedure is the fully-qualified name of the AuthService's GetSession RPC.
	AuthServiceGetSessionProcedure = "/api.users.v1.AuthService/GetSession"
	// AuthServiceSendSignupEmailVerificationCodeProcedure is the fully-qualified name of the
	// AuthService's SendSignupEmailVerificationCode RPC.
	AuthServiceSendSignupEmailVerificationCodeProcedure = "/api.users.v1.AuthService/SendSignupEmailVerificationCode"
	// AuthServiceVerifySignupEmailProcedure is the fully-qualified name of the AuthService's
	// VerifySignupEmail RPC.
	AuthServiceVerifySignupEmailProcedure = "/api.users.v1.AuthService/VerifySignupEmail"
	// AuthServiceSignupWithEmailProcedure is the fully-qualified name of the AuthService's
	// SignupWithEmail RPC.
	AuthServiceSignupWithEmailProcedure = "/api.users.v1.AuthService/SignupWithEmail"
	// AuthServiceSendForgotPasswordEmailProcedure is the fully-qualified name of the AuthService's
	// SendForgotPasswordEmail RPC.
	AuthServiceSendForgotPasswordEmailProcedure = "/api.users.v1.AuthService/SendForgotPasswordEmail"
	// AuthServiceResetPasswordProcedure is the fully-qualified name of the AuthService's ResetPassword
	// RPC.
	AuthServiceResetPasswordProcedure = "/api.users.v1.AuthService/ResetPassword"
	// AuthServiceChangePasswordProcedure is the fully-qualified name of the AuthService's
	// ChangePassword RPC.
	AuthServiceChangePasswordProcedure = "/api.users.v1.AuthService/ChangePassword"
	// AuthServiceChangeEmailVerificationProcedure is the fully-qualified name of the AuthService's
	// ChangeEmailVerification RPC.
	AuthServiceChangeEmailVerificationProcedure = "/api.users.v1.AuthService/ChangeEmailVerification"
	// AuthServiceVerifyChangeEmailProcedure is the fully-qualified name of the AuthService's
	// VerifyChangeEmail RPC.
	AuthServiceVerifyChangeEmailProcedure = "/api.users.v1.AuthService/VerifyChangeEmail"
	// AuthServiceSendVerifyNewEmailProcedure is the fully-qualified name of the AuthService's
	// SendVerifyNewEmail RPC.
	AuthServiceSendVerifyNewEmailProcedure = "/api.users.v1.AuthService/SendVerifyNewEmail"
	// AuthServiceVerifyNewEmailProcedure is the fully-qualified name of the AuthService's
	// VerifyNewEmail RPC.
	AuthServiceVerifyNewEmailProcedure = "/api.users.v1.AuthService/VerifyNewEmail"
	// AuthServiceUpdateUserDetailsProcedure is the fully-qualified name of the AuthService's
	// UpdateUserDetails RPC.
	AuthServiceUpdateUserDetailsProcedure = "/api.users.v1.AuthService/UpdateUserDetails"
	// AuthServiceUpdateUserImageProcedure is the fully-qualified name of the AuthService's
	// UpdateUserImage RPC.
	AuthServiceUpdateUserImageProcedure = "/api.users.v1.AuthService/UpdateUserImage"
	// AuthServiceListSessionsProcedure is the fully-qualified name of the AuthService's ListSessions
	// RPC.
	AuthServiceListSessionsProcedure = "/api.users.v1.AuthService/ListSessions"
	// AuthServiceRevokeSessionsProcedure is the fully-qualified name of the AuthService's
	// RevokeSessions RPC.
	AuthServiceRevokeSessionsProcedure = "/api.users.v1.AuthService/RevokeSessions"
	// AuthServiceRevokeOtherSessionsProcedure is the fully-qualified name of the AuthService's
	// RevokeOtherSessions RPC.
	AuthServiceRevokeOtherSessionsProcedure = "/api.users.v1.AuthService/RevokeOtherSessions"
	// AuthServiceLinkSocialProcedure is the fully-qualified name of the AuthService's LinkSocial RPC.
	AuthServiceLinkSocialProcedure = "/api.users.v1.AuthService/LinkSocial"
	// AuthServiceListAccountsProcedure is the fully-qualified name of the AuthService's ListAccounts
	// RPC.
	AuthServiceListAccountsProcedure = "/api.users.v1.AuthService/ListAccounts"
	// AuthServiceUnlinkAccountProcedure is the fully-qualified name of the AuthService's UnlinkAccount
	// RPC.
	AuthServiceUnlinkAccountProcedure = "/api.users.v1.AuthService/UnlinkAccount"
)

// AuthServiceClient is a client for the api.users.v1.AuthService service.
type AuthServiceClient interface {
	SignInWithEmail(context.Context, *connect.Request[v1.SignInWithEmailRequest]) (*connect.Response[v1.SignInWithEmailResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.RefreshTokenRequest]) (*connect.Response[v1.RefreshTokenResponse], error)
	SignInWithSocial(context.Context, *connect.Request[v1.SignInWithSocialRequest]) (*connect.Response[v1.SignInWithSocialResponse], error)
	SignOut(context.Context, *connect.Request[v1.SignOutRequest]) (*connect.Response[v1.SignOutResponse], error)
	GetSession(context.Context, *connect.Request[v1.GetSessionRequest]) (*connect.Response[v1.GetSessionResponse], error)
	SendSignupEmailVerificationCode(context.Context, *connect.Request[v1.SendSignupEmailVerificationCodeRequest]) (*connect.Response[v1.SendSignupEmailVerificationCodeResponse], error)
	VerifySignupEmail(context.Context, *connect.Request[v1.VerifySignupEmailRequest]) (*connect.Response[v1.VerifySignupEmailResponse], error)
	SignupWithEmail(context.Context, *connect.Request[v1.SignupWithEmailRequest]) (*connect.Response[v1.SignupWithEmailResponse], error)
	SendForgotPasswordEmail(context.Context, *connect.Request[v1.SendForgotPasswordEmailRequest]) (*connect.Response[v1.SendForgotPasswordEmailResponse], error)
	ResetPassword(context.Context, *connect.Request[v1.ResetPasswordRequest]) (*connect.Response[v1.ResetPasswordResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.ChangePasswordRequest]) (*connect.Response[v1.ChangePasswordResponse], error)
	ChangeEmailVerification(context.Context, *connect.Request[v1.ChangeEmailVerificationRequest]) (*connect.Response[v1.ChangeEmailVerificationResponse], error)
	VerifyChangeEmail(context.Context, *connect.Request[v1.VerifyChangeEmailRequest]) (*connect.Response[v1.VerifyChangeEmailResponse], error)
	SendVerifyNewEmail(context.Context, *connect.Request[v1.SendVerifyNewEmailRequest]) (*connect.Response[v1.SendVerifyNewEmailResponse], error)
	VerifyNewEmail(context.Context, *connect.Request[v1.VerifyNewEmailRequest]) (*connect.Response[v1.VerifyNewEmailResponse], error)
	UpdateUserDetails(context.Context, *connect.Request[v1.UpdateUserDetailsRequest]) (*connect.Response[v1.UpdateUserDetailsResponse], error)
	UpdateUserImage(context.Context, *connect.Request[v1.UpdateUserImageRequest]) (*connect.Response[v1.UpdateUserImageResponse], error)
	ListSessions(context.Context, *connect.Request[v1.ListSessionsRequest]) (*connect.Response[v1.ListSessionsResponse], error)
	RevokeSessions(context.Context, *connect.Request[v1.RevokeSessionsRequest]) (*connect.Response[v1.RevokeSessionsResponse], error)
	RevokeOtherSessions(context.Context, *connect.Request[v1.RevokeOtherSessionsRequest]) (*connect.Response[v1.RevokeOtherSessionsResponse], error)
	LinkSocial(context.Context, *connect.Request[v1.LinkSocialRequest]) (*connect.Response[v1.LinkSocialResponse], error)
	ListAccounts(context.Context, *connect.Request[v1.ListAccountsRequest]) (*connect.Response[v1.ListAccountsResponse], error)
	UnlinkAccount(context.Context, *connect.Request[v1.UnlinkAccountRequest]) (*connect.Response[v1.UnlinkAccountResponse], error)
}

// NewAuthServiceClient constructs a client for the api.users.v1.AuthService service. By default, it
// uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewAuthServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) AuthServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	authServiceMethods := v1.File_users_v1_auth_proto.Services().ByName("AuthService").Methods()
	return &authServiceClient{
		signInWithEmail: connect.NewClient[v1.SignInWithEmailRequest, v1.SignInWithEmailResponse](
			httpClient,
			baseURL+AuthServiceSignInWithEmailProcedure,
			connect.WithSchema(authServiceMethods.ByName("SignInWithEmail")),
			connect.WithClientOptions(opts...),
		),
		refreshToken: connect.NewClient[v1.RefreshTokenRequest, v1.RefreshTokenResponse](
			httpClient,
			baseURL+AuthServiceRefreshTokenProcedure,
			connect.WithSchema(authServiceMethods.ByName("RefreshToken")),
			connect.WithClientOptions(opts...),
		),
		signInWithSocial: connect.NewClient[v1.SignInWithSocialRequest, v1.SignInWithSocialResponse](
			httpClient,
			baseURL+AuthServiceSignInWithSocialProcedure,
			connect.WithSchema(authServiceMethods.ByName("SignInWithSocial")),
			connect.WithClientOptions(opts...),
		),
		signOut: connect.NewClient[v1.SignOutRequest, v1.SignOutResponse](
			httpClient,
			baseURL+AuthServiceSignOutProcedure,
			connect.WithSchema(authServiceMethods.ByName("SignOut")),
			connect.WithClientOptions(opts...),
		),
		getSession: connect.NewClient[v1.GetSessionRequest, v1.GetSessionResponse](
			httpClient,
			baseURL+AuthServiceGetSessionProcedure,
			connect.WithSchema(authServiceMethods.ByName("GetSession")),
			connect.WithClientOptions(opts...),
		),
		sendSignupEmailVerificationCode: connect.NewClient[v1.SendSignupEmailVerificationCodeRequest, v1.SendSignupEmailVerificationCodeResponse](
			httpClient,
			baseURL+AuthServiceSendSignupEmailVerificationCodeProcedure,
			connect.WithSchema(authServiceMethods.ByName("SendSignupEmailVerificationCode")),
			connect.WithClientOptions(opts...),
		),
		verifySignupEmail: connect.NewClient[v1.VerifySignupEmailRequest, v1.VerifySignupEmailResponse](
			httpClient,
			baseURL+AuthServiceVerifySignupEmailProcedure,
			connect.WithSchema(authServiceMethods.ByName("VerifySignupEmail")),
			connect.WithClientOptions(opts...),
		),
		signupWithEmail: connect.NewClient[v1.SignupWithEmailRequest, v1.SignupWithEmailResponse](
			httpClient,
			baseURL+AuthServiceSignupWithEmailProcedure,
			connect.WithSchema(authServiceMethods.ByName("SignupWithEmail")),
			connect.WithClientOptions(opts...),
		),
		sendForgotPasswordEmail: connect.NewClient[v1.SendForgotPasswordEmailRequest, v1.SendForgotPasswordEmailResponse](
			httpClient,
			baseURL+AuthServiceSendForgotPasswordEmailProcedure,
			connect.WithSchema(authServiceMethods.ByName("SendForgotPasswordEmail")),
			connect.WithClientOptions(opts...),
		),
		resetPassword: connect.NewClient[v1.ResetPasswordRequest, v1.ResetPasswordResponse](
			httpClient,
			baseURL+AuthServiceResetPasswordProcedure,
			connect.WithSchema(authServiceMethods.ByName("ResetPassword")),
			connect.WithClientOptions(opts...),
		),
		changePassword: connect.NewClient[v1.ChangePasswordRequest, v1.ChangePasswordResponse](
			httpClient,
			baseURL+AuthServiceChangePasswordProcedure,
			connect.WithSchema(authServiceMethods.ByName("ChangePassword")),
			connect.WithClientOptions(opts...),
		),
		changeEmailVerification: connect.NewClient[v1.ChangeEmailVerificationRequest, v1.ChangeEmailVerificationResponse](
			httpClient,
			baseURL+AuthServiceChangeEmailVerificationProcedure,
			connect.WithSchema(authServiceMethods.ByName("ChangeEmailVerification")),
			connect.WithClientOptions(opts...),
		),
		verifyChangeEmail: connect.NewClient[v1.VerifyChangeEmailRequest, v1.VerifyChangeEmailResponse](
			httpClient,
			baseURL+AuthServiceVerifyChangeEmailProcedure,
			connect.WithSchema(authServiceMethods.ByName("VerifyChangeEmail")),
			connect.WithClientOptions(opts...),
		),
		sendVerifyNewEmail: connect.NewClient[v1.SendVerifyNewEmailRequest, v1.SendVerifyNewEmailResponse](
			httpClient,
			baseURL+AuthServiceSendVerifyNewEmailProcedure,
			connect.WithSchema(authServiceMethods.ByName("SendVerifyNewEmail")),
			connect.WithClientOptions(opts...),
		),
		verifyNewEmail: connect.NewClient[v1.VerifyNewEmailRequest, v1.VerifyNewEmailResponse](
			httpClient,
			baseURL+AuthServiceVerifyNewEmailProcedure,
			connect.WithSchema(authServiceMethods.ByName("VerifyNewEmail")),
			connect.WithClientOptions(opts...),
		),
		updateUserDetails: connect.NewClient[v1.UpdateUserDetailsRequest, v1.UpdateUserDetailsResponse](
			httpClient,
			baseURL+AuthServiceUpdateUserDetailsProcedure,
			connect.WithSchema(authServiceMethods.ByName("UpdateUserDetails")),
			connect.WithClientOptions(opts...),
		),
		updateUserImage: connect.NewClient[v1.UpdateUserImageRequest, v1.UpdateUserImageResponse](
			httpClient,
			baseURL+AuthServiceUpdateUserImageProcedure,
			connect.WithSchema(authServiceMethods.ByName("UpdateUserImage")),
			connect.WithClientOptions(opts...),
		),
		listSessions: connect.NewClient[v1.ListSessionsRequest, v1.ListSessionsResponse](
			httpClient,
			baseURL+AuthServiceListSessionsProcedure,
			connect.WithSchema(authServiceMethods.ByName("ListSessions")),
			connect.WithClientOptions(opts...),
		),
		revokeSessions: connect.NewClient[v1.RevokeSessionsRequest, v1.RevokeSessionsResponse](
			httpClient,
			baseURL+AuthServiceRevokeSessionsProcedure,
			connect.WithSchema(authServiceMethods.ByName("RevokeSessions")),
			connect.WithClientOptions(opts...),
		),
		revokeOtherSessions: connect.NewClient[v1.RevokeOtherSessionsRequest, v1.RevokeOtherSessionsResponse](
			httpClient,
			baseURL+AuthServiceRevokeOtherSessionsProcedure,
			connect.WithSchema(authServiceMethods.ByName("RevokeOtherSessions")),
			connect.WithClientOptions(opts...),
		),
		linkSocial: connect.NewClient[v1.LinkSocialRequest, v1.LinkSocialResponse](
			httpClient,
			baseURL+AuthServiceLinkSocialProcedure,
			connect.WithSchema(authServiceMethods.ByName("LinkSocial")),
			connect.WithClientOptions(opts...),
		),
		listAccounts: connect.NewClient[v1.ListAccountsRequest, v1.ListAccountsResponse](
			httpClient,
			baseURL+AuthServiceListAccountsProcedure,
			connect.WithSchema(authServiceMethods.ByName("ListAccounts")),
			connect.WithClientOptions(opts...),
		),
		unlinkAccount: connect.NewClient[v1.UnlinkAccountRequest, v1.UnlinkAccountResponse](
			httpClient,
			baseURL+AuthServiceUnlinkAccountProcedure,
			connect.WithSchema(authServiceMethods.ByName("UnlinkAccount")),
			connect.WithClientOptions(opts...),
		),
	}
}

// authServiceClient implements AuthServiceClient.
type authServiceClient struct {
	signInWithEmail                 *connect.Client[v1.SignInWithEmailRequest, v1.SignInWithEmailResponse]
	refreshToken                    *connect.Client[v1.RefreshTokenRequest, v1.RefreshTokenResponse]
	signInWithSocial                *connect.Client[v1.SignInWithSocialRequest, v1.SignInWithSocialResponse]
	signOut                         *connect.Client[v1.SignOutRequest, v1.SignOutResponse]
	getSession                      *connect.Client[v1.GetSessionRequest, v1.GetSessionResponse]
	sendSignupEmailVerificationCode *connect.Client[v1.SendSignupEmailVerificationCodeRequest, v1.SendSignupEmailVerificationCodeResponse]
	verifySignupEmail               *connect.Client[v1.VerifySignupEmailRequest, v1.VerifySignupEmailResponse]
	signupWithEmail                 *connect.Client[v1.SignupWithEmailRequest, v1.SignupWithEmailResponse]
	sendForgotPasswordEmail         *connect.Client[v1.SendForgotPasswordEmailRequest, v1.SendForgotPasswordEmailResponse]
	resetPassword                   *connect.Client[v1.ResetPasswordRequest, v1.ResetPasswordResponse]
	changePassword                  *connect.Client[v1.ChangePasswordRequest, v1.ChangePasswordResponse]
	changeEmailVerification         *connect.Client[v1.ChangeEmailVerificationRequest, v1.ChangeEmailVerificationResponse]
	verifyChangeEmail               *connect.Client[v1.VerifyChangeEmailRequest, v1.VerifyChangeEmailResponse]
	sendVerifyNewEmail              *connect.Client[v1.SendVerifyNewEmailRequest, v1.SendVerifyNewEmailResponse]
	verifyNewEmail                  *connect.Client[v1.VerifyNewEmailRequest, v1.VerifyNewEmailResponse]
	updateUserDetails               *connect.Client[v1.UpdateUserDetailsRequest, v1.UpdateUserDetailsResponse]
	updateUserImage                 *connect.Client[v1.UpdateUserImageRequest, v1.UpdateUserImageResponse]
	listSessions                    *connect.Client[v1.ListSessionsRequest, v1.ListSessionsResponse]
	revokeSessions                  *connect.Client[v1.RevokeSessionsRequest, v1.RevokeSessionsResponse]
	revokeOtherSessions             *connect.Client[v1.RevokeOtherSessionsRequest, v1.RevokeOtherSessionsResponse]
	linkSocial                      *connect.Client[v1.LinkSocialRequest, v1.LinkSocialResponse]
	listAccounts                    *connect.Client[v1.ListAccountsRequest, v1.ListAccountsResponse]
	unlinkAccount                   *connect.Client[v1.UnlinkAccountRequest, v1.UnlinkAccountResponse]
}

// SignInWithEmail calls api.users.v1.AuthService.SignInWithEmail.
func (c *authServiceClient) SignInWithEmail(ctx context.Context, req *connect.Request[v1.SignInWithEmailRequest]) (*connect.Response[v1.SignInWithEmailResponse], error) {
	return c.signInWithEmail.CallUnary(ctx, req)
}

// RefreshToken calls api.users.v1.AuthService.RefreshToken.
func (c *authServiceClient) RefreshToken(ctx context.Context, req *connect.Request[v1.RefreshTokenRequest]) (*connect.Response[v1.RefreshTokenResponse], error) {
	return c.refreshToken.CallUnary(ctx, req)
}

// SignInWithSocial calls api.users.v1.AuthService.SignInWithSocial.
func (c *authServiceClient) SignInWithSocial(ctx context.Context, req *connect.Request[v1.SignInWithSocialRequest]) (*connect.Response[v1.SignInWithSocialResponse], error) {
	return c.signInWithSocial.CallUnary(ctx, req)
}

// SignOut calls api.users.v1.AuthService.SignOut.
func (c *authServiceClient) SignOut(ctx context.Context, req *connect.Request[v1.SignOutRequest]) (*connect.Response[v1.SignOutResponse], error) {
	return c.signOut.CallUnary(ctx, req)
}

// GetSession calls api.users.v1.AuthService.GetSession.
func (c *authServiceClient) GetSession(ctx context.Context, req *connect.Request[v1.GetSessionRequest]) (*connect.Response[v1.GetSessionResponse], error) {
	return c.getSession.CallUnary(ctx, req)
}

// SendSignupEmailVerificationCode calls api.users.v1.AuthService.SendSignupEmailVerificationCode.
func (c *authServiceClient) SendSignupEmailVerificationCode(ctx context.Context, req *connect.Request[v1.SendSignupEmailVerificationCodeRequest]) (*connect.Response[v1.SendSignupEmailVerificationCodeResponse], error) {
	return c.sendSignupEmailVerificationCode.CallUnary(ctx, req)
}

// VerifySignupEmail calls api.users.v1.AuthService.VerifySignupEmail.
func (c *authServiceClient) VerifySignupEmail(ctx context.Context, req *connect.Request[v1.VerifySignupEmailRequest]) (*connect.Response[v1.VerifySignupEmailResponse], error) {
	return c.verifySignupEmail.CallUnary(ctx, req)
}

// SignupWithEmail calls api.users.v1.AuthService.SignupWithEmail.
func (c *authServiceClient) SignupWithEmail(ctx context.Context, req *connect.Request[v1.SignupWithEmailRequest]) (*connect.Response[v1.SignupWithEmailResponse], error) {
	return c.signupWithEmail.CallUnary(ctx, req)
}

// SendForgotPasswordEmail calls api.users.v1.AuthService.SendForgotPasswordEmail.
func (c *authServiceClient) SendForgotPasswordEmail(ctx context.Context, req *connect.Request[v1.SendForgotPasswordEmailRequest]) (*connect.Response[v1.SendForgotPasswordEmailResponse], error) {
	return c.sendForgotPasswordEmail.CallUnary(ctx, req)
}

// ResetPassword calls api.users.v1.AuthService.ResetPassword.
func (c *authServiceClient) ResetPassword(ctx context.Context, req *connect.Request[v1.ResetPasswordRequest]) (*connect.Response[v1.ResetPasswordResponse], error) {
	return c.resetPassword.CallUnary(ctx, req)
}

// ChangePassword calls api.users.v1.AuthService.ChangePassword.
func (c *authServiceClient) ChangePassword(ctx context.Context, req *connect.Request[v1.ChangePasswordRequest]) (*connect.Response[v1.ChangePasswordResponse], error) {
	return c.changePassword.CallUnary(ctx, req)
}

// ChangeEmailVerification calls api.users.v1.AuthService.ChangeEmailVerification.
func (c *authServiceClient) ChangeEmailVerification(ctx context.Context, req *connect.Request[v1.ChangeEmailVerificationRequest]) (*connect.Response[v1.ChangeEmailVerificationResponse], error) {
	return c.changeEmailVerification.CallUnary(ctx, req)
}

// VerifyChangeEmail calls api.users.v1.AuthService.VerifyChangeEmail.
func (c *authServiceClient) VerifyChangeEmail(ctx context.Context, req *connect.Request[v1.VerifyChangeEmailRequest]) (*connect.Response[v1.VerifyChangeEmailResponse], error) {
	return c.verifyChangeEmail.CallUnary(ctx, req)
}

// SendVerifyNewEmail calls api.users.v1.AuthService.SendVerifyNewEmail.
func (c *authServiceClient) SendVerifyNewEmail(ctx context.Context, req *connect.Request[v1.SendVerifyNewEmailRequest]) (*connect.Response[v1.SendVerifyNewEmailResponse], error) {
	return c.sendVerifyNewEmail.CallUnary(ctx, req)
}

// VerifyNewEmail calls api.users.v1.AuthService.VerifyNewEmail.
func (c *authServiceClient) VerifyNewEmail(ctx context.Context, req *connect.Request[v1.VerifyNewEmailRequest]) (*connect.Response[v1.VerifyNewEmailResponse], error) {
	return c.verifyNewEmail.CallUnary(ctx, req)
}

// UpdateUserDetails calls api.users.v1.AuthService.UpdateUserDetails.
func (c *authServiceClient) UpdateUserDetails(ctx context.Context, req *connect.Request[v1.UpdateUserDetailsRequest]) (*connect.Response[v1.UpdateUserDetailsResponse], error) {
	return c.updateUserDetails.CallUnary(ctx, req)
}

// UpdateUserImage calls api.users.v1.AuthService.UpdateUserImage.
func (c *authServiceClient) UpdateUserImage(ctx context.Context, req *connect.Request[v1.UpdateUserImageRequest]) (*connect.Response[v1.UpdateUserImageResponse], error) {
	return c.updateUserImage.CallUnary(ctx, req)
}

// ListSessions calls api.users.v1.AuthService.ListSessions.
func (c *authServiceClient) ListSessions(ctx context.Context, req *connect.Request[v1.ListSessionsRequest]) (*connect.Response[v1.ListSessionsResponse], error) {
	return c.listSessions.CallUnary(ctx, req)
}

// RevokeSessions calls api.users.v1.AuthService.RevokeSessions.
func (c *authServiceClient) RevokeSessions(ctx context.Context, req *connect.Request[v1.RevokeSessionsRequest]) (*connect.Response[v1.RevokeSessionsResponse], error) {
	return c.revokeSessions.CallUnary(ctx, req)
}

// RevokeOtherSessions calls api.users.v1.AuthService.RevokeOtherSessions.
func (c *authServiceClient) RevokeOtherSessions(ctx context.Context, req *connect.Request[v1.RevokeOtherSessionsRequest]) (*connect.Response[v1.RevokeOtherSessionsResponse], error) {
	return c.revokeOtherSessions.CallUnary(ctx, req)
}

// LinkSocial calls api.users.v1.AuthService.LinkSocial.
func (c *authServiceClient) LinkSocial(ctx context.Context, req *connect.Request[v1.LinkSocialRequest]) (*connect.Response[v1.LinkSocialResponse], error) {
	return c.linkSocial.CallUnary(ctx, req)
}

// ListAccounts calls api.users.v1.AuthService.ListAccounts.
func (c *authServiceClient) ListAccounts(ctx context.Context, req *connect.Request[v1.ListAccountsRequest]) (*connect.Response[v1.ListAccountsResponse], error) {
	return c.listAccounts.CallUnary(ctx, req)
}

// UnlinkAccount calls api.users.v1.AuthService.UnlinkAccount.
func (c *authServiceClient) UnlinkAccount(ctx context.Context, req *connect.Request[v1.UnlinkAccountRequest]) (*connect.Response[v1.UnlinkAccountResponse], error) {
	return c.unlinkAccount.CallUnary(ctx, req)
}

// AuthServiceHandler is an implementation of the api.users.v1.AuthService service.
type AuthServiceHandler interface {
	SignInWithEmail(context.Context, *connect.Request[v1.SignInWithEmailRequest]) (*connect.Response[v1.SignInWithEmailResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.RefreshTokenRequest]) (*connect.Response[v1.RefreshTokenResponse], error)
	SignInWithSocial(context.Context, *connect.Request[v1.SignInWithSocialRequest]) (*connect.Response[v1.SignInWithSocialResponse], error)
	SignOut(context.Context, *connect.Request[v1.SignOutRequest]) (*connect.Response[v1.SignOutResponse], error)
	GetSession(context.Context, *connect.Request[v1.GetSessionRequest]) (*connect.Response[v1.GetSessionResponse], error)
	SendSignupEmailVerificationCode(context.Context, *connect.Request[v1.SendSignupEmailVerificationCodeRequest]) (*connect.Response[v1.SendSignupEmailVerificationCodeResponse], error)
	VerifySignupEmail(context.Context, *connect.Request[v1.VerifySignupEmailRequest]) (*connect.Response[v1.VerifySignupEmailResponse], error)
	SignupWithEmail(context.Context, *connect.Request[v1.SignupWithEmailRequest]) (*connect.Response[v1.SignupWithEmailResponse], error)
	SendForgotPasswordEmail(context.Context, *connect.Request[v1.SendForgotPasswordEmailRequest]) (*connect.Response[v1.SendForgotPasswordEmailResponse], error)
	ResetPassword(context.Context, *connect.Request[v1.ResetPasswordRequest]) (*connect.Response[v1.ResetPasswordResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.ChangePasswordRequest]) (*connect.Response[v1.ChangePasswordResponse], error)
	ChangeEmailVerification(context.Context, *connect.Request[v1.ChangeEmailVerificationRequest]) (*connect.Response[v1.ChangeEmailVerificationResponse], error)
	VerifyChangeEmail(context.Context, *connect.Request[v1.VerifyChangeEmailRequest]) (*connect.Response[v1.VerifyChangeEmailResponse], error)
	SendVerifyNewEmail(context.Context, *connect.Request[v1.SendVerifyNewEmailRequest]) (*connect.Response[v1.SendVerifyNewEmailResponse], error)
	VerifyNewEmail(context.Context, *connect.Request[v1.VerifyNewEmailRequest]) (*connect.Response[v1.VerifyNewEmailResponse], error)
	UpdateUserDetails(context.Context, *connect.Request[v1.UpdateUserDetailsRequest]) (*connect.Response[v1.UpdateUserDetailsResponse], error)
	UpdateUserImage(context.Context, *connect.Request[v1.UpdateUserImageRequest]) (*connect.Response[v1.UpdateUserImageResponse], error)
	ListSessions(context.Context, *connect.Request[v1.ListSessionsRequest]) (*connect.Response[v1.ListSessionsResponse], error)
	RevokeSessions(context.Context, *connect.Request[v1.RevokeSessionsRequest]) (*connect.Response[v1.RevokeSessionsResponse], error)
	RevokeOtherSessions(context.Context, *connect.Request[v1.RevokeOtherSessionsRequest]) (*connect.Response[v1.RevokeOtherSessionsResponse], error)
	LinkSocial(context.Context, *connect.Request[v1.LinkSocialRequest]) (*connect.Response[v1.LinkSocialResponse], error)
	ListAccounts(context.Context, *connect.Request[v1.ListAccountsRequest]) (*connect.Response[v1.ListAccountsResponse], error)
	UnlinkAccount(context.Context, *connect.Request[v1.UnlinkAccountRequest]) (*connect.Response[v1.UnlinkAccountResponse], error)
}

// NewAuthServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewAuthServiceHandler(svc AuthServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	authServiceMethods := v1.File_users_v1_auth_proto.Services().ByName("AuthService").Methods()
	authServiceSignInWithEmailHandler := connect.NewUnaryHandler(
		AuthServiceSignInWithEmailProcedure,
		svc.SignInWithEmail,
		connect.WithSchema(authServiceMethods.ByName("SignInWithEmail")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceRefreshTokenHandler := connect.NewUnaryHandler(
		AuthServiceRefreshTokenProcedure,
		svc.RefreshToken,
		connect.WithSchema(authServiceMethods.ByName("RefreshToken")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceSignInWithSocialHandler := connect.NewUnaryHandler(
		AuthServiceSignInWithSocialProcedure,
		svc.SignInWithSocial,
		connect.WithSchema(authServiceMethods.ByName("SignInWithSocial")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceSignOutHandler := connect.NewUnaryHandler(
		AuthServiceSignOutProcedure,
		svc.SignOut,
		connect.WithSchema(authServiceMethods.ByName("SignOut")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceGetSessionHandler := connect.NewUnaryHandler(
		AuthServiceGetSessionProcedure,
		svc.GetSession,
		connect.WithSchema(authServiceMethods.ByName("GetSession")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceSendSignupEmailVerificationCodeHandler := connect.NewUnaryHandler(
		AuthServiceSendSignupEmailVerificationCodeProcedure,
		svc.SendSignupEmailVerificationCode,
		connect.WithSchema(authServiceMethods.ByName("SendSignupEmailVerificationCode")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceVerifySignupEmailHandler := connect.NewUnaryHandler(
		AuthServiceVerifySignupEmailProcedure,
		svc.VerifySignupEmail,
		connect.WithSchema(authServiceMethods.ByName("VerifySignupEmail")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceSignupWithEmailHandler := connect.NewUnaryHandler(
		AuthServiceSignupWithEmailProcedure,
		svc.SignupWithEmail,
		connect.WithSchema(authServiceMethods.ByName("SignupWithEmail")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceSendForgotPasswordEmailHandler := connect.NewUnaryHandler(
		AuthServiceSendForgotPasswordEmailProcedure,
		svc.SendForgotPasswordEmail,
		connect.WithSchema(authServiceMethods.ByName("SendForgotPasswordEmail")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceResetPasswordHandler := connect.NewUnaryHandler(
		AuthServiceResetPasswordProcedure,
		svc.ResetPassword,
		connect.WithSchema(authServiceMethods.ByName("ResetPassword")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceChangePasswordHandler := connect.NewUnaryHandler(
		AuthServiceChangePasswordProcedure,
		svc.ChangePassword,
		connect.WithSchema(authServiceMethods.ByName("ChangePassword")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceChangeEmailVerificationHandler := connect.NewUnaryHandler(
		AuthServiceChangeEmailVerificationProcedure,
		svc.ChangeEmailVerification,
		connect.WithSchema(authServiceMethods.ByName("ChangeEmailVerification")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceVerifyChangeEmailHandler := connect.NewUnaryHandler(
		AuthServiceVerifyChangeEmailProcedure,
		svc.VerifyChangeEmail,
		connect.WithSchema(authServiceMethods.ByName("VerifyChangeEmail")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceSendVerifyNewEmailHandler := connect.NewUnaryHandler(
		AuthServiceSendVerifyNewEmailProcedure,
		svc.SendVerifyNewEmail,
		connect.WithSchema(authServiceMethods.ByName("SendVerifyNewEmail")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceVerifyNewEmailHandler := connect.NewUnaryHandler(
		AuthServiceVerifyNewEmailProcedure,
		svc.VerifyNewEmail,
		connect.WithSchema(authServiceMethods.ByName("VerifyNewEmail")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceUpdateUserDetailsHandler := connect.NewUnaryHandler(
		AuthServiceUpdateUserDetailsProcedure,
		svc.UpdateUserDetails,
		connect.WithSchema(authServiceMethods.ByName("UpdateUserDetails")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceUpdateUserImageHandler := connect.NewUnaryHandler(
		AuthServiceUpdateUserImageProcedure,
		svc.UpdateUserImage,
		connect.WithSchema(authServiceMethods.ByName("UpdateUserImage")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceListSessionsHandler := connect.NewUnaryHandler(
		AuthServiceListSessionsProcedure,
		svc.ListSessions,
		connect.WithSchema(authServiceMethods.ByName("ListSessions")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceRevokeSessionsHandler := connect.NewUnaryHandler(
		AuthServiceRevokeSessionsProcedure,
		svc.RevokeSessions,
		connect.WithSchema(authServiceMethods.ByName("RevokeSessions")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceRevokeOtherSessionsHandler := connect.NewUnaryHandler(
		AuthServiceRevokeOtherSessionsProcedure,
		svc.RevokeOtherSessions,
		connect.WithSchema(authServiceMethods.ByName("RevokeOtherSessions")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceLinkSocialHandler := connect.NewUnaryHandler(
		AuthServiceLinkSocialProcedure,
		svc.LinkSocial,
		connect.WithSchema(authServiceMethods.ByName("LinkSocial")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceListAccountsHandler := connect.NewUnaryHandler(
		AuthServiceListAccountsProcedure,
		svc.ListAccounts,
		connect.WithSchema(authServiceMethods.ByName("ListAccounts")),
		connect.WithHandlerOptions(opts...),
	)
	authServiceUnlinkAccountHandler := connect.NewUnaryHandler(
		AuthServiceUnlinkAccountProcedure,
		svc.UnlinkAccount,
		connect.WithSchema(authServiceMethods.ByName("UnlinkAccount")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.users.v1.AuthService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case AuthServiceSignInWithEmailProcedure:
			authServiceSignInWithEmailHandler.ServeHTTP(w, r)
		case AuthServiceRefreshTokenProcedure:
			authServiceRefreshTokenHandler.ServeHTTP(w, r)
		case AuthServiceSignInWithSocialProcedure:
			authServiceSignInWithSocialHandler.ServeHTTP(w, r)
		case AuthServiceSignOutProcedure:
			authServiceSignOutHandler.ServeHTTP(w, r)
		case AuthServiceGetSessionProcedure:
			authServiceGetSessionHandler.ServeHTTP(w, r)
		case AuthServiceSendSignupEmailVerificationCodeProcedure:
			authServiceSendSignupEmailVerificationCodeHandler.ServeHTTP(w, r)
		case AuthServiceVerifySignupEmailProcedure:
			authServiceVerifySignupEmailHandler.ServeHTTP(w, r)
		case AuthServiceSignupWithEmailProcedure:
			authServiceSignupWithEmailHandler.ServeHTTP(w, r)
		case AuthServiceSendForgotPasswordEmailProcedure:
			authServiceSendForgotPasswordEmailHandler.ServeHTTP(w, r)
		case AuthServiceResetPasswordProcedure:
			authServiceResetPasswordHandler.ServeHTTP(w, r)
		case AuthServiceChangePasswordProcedure:
			authServiceChangePasswordHandler.ServeHTTP(w, r)
		case AuthServiceChangeEmailVerificationProcedure:
			authServiceChangeEmailVerificationHandler.ServeHTTP(w, r)
		case AuthServiceVerifyChangeEmailProcedure:
			authServiceVerifyChangeEmailHandler.ServeHTTP(w, r)
		case AuthServiceSendVerifyNewEmailProcedure:
			authServiceSendVerifyNewEmailHandler.ServeHTTP(w, r)
		case AuthServiceVerifyNewEmailProcedure:
			authServiceVerifyNewEmailHandler.ServeHTTP(w, r)
		case AuthServiceUpdateUserDetailsProcedure:
			authServiceUpdateUserDetailsHandler.ServeHTTP(w, r)
		case AuthServiceUpdateUserImageProcedure:
			authServiceUpdateUserImageHandler.ServeHTTP(w, r)
		case AuthServiceListSessionsProcedure:
			authServiceListSessionsHandler.ServeHTTP(w, r)
		case AuthServiceRevokeSessionsProcedure:
			authServiceRevokeSessionsHandler.ServeHTTP(w, r)
		case AuthServiceRevokeOtherSessionsProcedure:
			authServiceRevokeOtherSessionsHandler.ServeHTTP(w, r)
		case AuthServiceLinkSocialProcedure:
			authServiceLinkSocialHandler.ServeHTTP(w, r)
		case AuthServiceListAccountsProcedure:
			authServiceListAccountsHandler.ServeHTTP(w, r)
		case AuthServiceUnlinkAccountProcedure:
			authServiceUnlinkAccountHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedAuthServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedAuthServiceHandler struct{}

func (UnimplementedAuthServiceHandler) SignInWithEmail(context.Context, *connect.Request[v1.SignInWithEmailRequest]) (*connect.Response[v1.SignInWithEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.SignInWithEmail is not implemented"))
}

func (UnimplementedAuthServiceHandler) RefreshToken(context.Context, *connect.Request[v1.RefreshTokenRequest]) (*connect.Response[v1.RefreshTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.RefreshToken is not implemented"))
}

func (UnimplementedAuthServiceHandler) SignInWithSocial(context.Context, *connect.Request[v1.SignInWithSocialRequest]) (*connect.Response[v1.SignInWithSocialResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.SignInWithSocial is not implemented"))
}

func (UnimplementedAuthServiceHandler) SignOut(context.Context, *connect.Request[v1.SignOutRequest]) (*connect.Response[v1.SignOutResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.SignOut is not implemented"))
}

func (UnimplementedAuthServiceHandler) GetSession(context.Context, *connect.Request[v1.GetSessionRequest]) (*connect.Response[v1.GetSessionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.GetSession is not implemented"))
}

func (UnimplementedAuthServiceHandler) SendSignupEmailVerificationCode(context.Context, *connect.Request[v1.SendSignupEmailVerificationCodeRequest]) (*connect.Response[v1.SendSignupEmailVerificationCodeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.SendSignupEmailVerificationCode is not implemented"))
}

func (UnimplementedAuthServiceHandler) VerifySignupEmail(context.Context, *connect.Request[v1.VerifySignupEmailRequest]) (*connect.Response[v1.VerifySignupEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.VerifySignupEmail is not implemented"))
}

func (UnimplementedAuthServiceHandler) SignupWithEmail(context.Context, *connect.Request[v1.SignupWithEmailRequest]) (*connect.Response[v1.SignupWithEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.SignupWithEmail is not implemented"))
}

func (UnimplementedAuthServiceHandler) SendForgotPasswordEmail(context.Context, *connect.Request[v1.SendForgotPasswordEmailRequest]) (*connect.Response[v1.SendForgotPasswordEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.SendForgotPasswordEmail is not implemented"))
}

func (UnimplementedAuthServiceHandler) ResetPassword(context.Context, *connect.Request[v1.ResetPasswordRequest]) (*connect.Response[v1.ResetPasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.ResetPassword is not implemented"))
}

func (UnimplementedAuthServiceHandler) ChangePassword(context.Context, *connect.Request[v1.ChangePasswordRequest]) (*connect.Response[v1.ChangePasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.ChangePassword is not implemented"))
}

func (UnimplementedAuthServiceHandler) ChangeEmailVerification(context.Context, *connect.Request[v1.ChangeEmailVerificationRequest]) (*connect.Response[v1.ChangeEmailVerificationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.ChangeEmailVerification is not implemented"))
}

func (UnimplementedAuthServiceHandler) VerifyChangeEmail(context.Context, *connect.Request[v1.VerifyChangeEmailRequest]) (*connect.Response[v1.VerifyChangeEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.VerifyChangeEmail is not implemented"))
}

func (UnimplementedAuthServiceHandler) SendVerifyNewEmail(context.Context, *connect.Request[v1.SendVerifyNewEmailRequest]) (*connect.Response[v1.SendVerifyNewEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.SendVerifyNewEmail is not implemented"))
}

func (UnimplementedAuthServiceHandler) VerifyNewEmail(context.Context, *connect.Request[v1.VerifyNewEmailRequest]) (*connect.Response[v1.VerifyNewEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.VerifyNewEmail is not implemented"))
}

func (UnimplementedAuthServiceHandler) UpdateUserDetails(context.Context, *connect.Request[v1.UpdateUserDetailsRequest]) (*connect.Response[v1.UpdateUserDetailsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.UpdateUserDetails is not implemented"))
}

func (UnimplementedAuthServiceHandler) UpdateUserImage(context.Context, *connect.Request[v1.UpdateUserImageRequest]) (*connect.Response[v1.UpdateUserImageResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.UpdateUserImage is not implemented"))
}

func (UnimplementedAuthServiceHandler) ListSessions(context.Context, *connect.Request[v1.ListSessionsRequest]) (*connect.Response[v1.ListSessionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.ListSessions is not implemented"))
}

func (UnimplementedAuthServiceHandler) RevokeSessions(context.Context, *connect.Request[v1.RevokeSessionsRequest]) (*connect.Response[v1.RevokeSessionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.RevokeSessions is not implemented"))
}

func (UnimplementedAuthServiceHandler) RevokeOtherSessions(context.Context, *connect.Request[v1.RevokeOtherSessionsRequest]) (*connect.Response[v1.RevokeOtherSessionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.RevokeOtherSessions is not implemented"))
}

func (UnimplementedAuthServiceHandler) LinkSocial(context.Context, *connect.Request[v1.LinkSocialRequest]) (*connect.Response[v1.LinkSocialResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.LinkSocial is not implemented"))
}

func (UnimplementedAuthServiceHandler) ListAccounts(context.Context, *connect.Request[v1.ListAccountsRequest]) (*connect.Response[v1.ListAccountsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.ListAccounts is not implemented"))
}

func (UnimplementedAuthServiceHandler) UnlinkAccount(context.Context, *connect.Request[v1.UnlinkAccountRequest]) (*connect.Response[v1.UnlinkAccountResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.users.v1.AuthService.UnlinkAccount is not implemented"))
}
