// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: taxonomy/v1/categories.proto

package taxonomyv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/taxonomy/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CategoryServiceName is the fully-qualified name of the CategoryService service.
	CategoryServiceName = "api.taxonomy.v1.CategoryService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CategoryServiceAddCategoryProcedure is the fully-qualified name of the CategoryService's
	// AddCategory RPC.
	CategoryServiceAddCategoryProcedure = "/api.taxonomy.v1.CategoryService/AddCategory"
	// CategoryServiceGetAllCategoriesProcedure is the fully-qualified name of the CategoryService's
	// GetAllCategories RPC.
	CategoryServiceGetAllCategoriesProcedure = "/api.taxonomy.v1.CategoryService/GetAllCategories"
	// CategoryServiceGetCategoryProcedure is the fully-qualified name of the CategoryService's
	// GetCategory RPC.
	CategoryServiceGetCategoryProcedure = "/api.taxonomy.v1.CategoryService/GetCategory"
	// CategoryServiceUpdateCategoryProcedure is the fully-qualified name of the CategoryService's
	// UpdateCategory RPC.
	CategoryServiceUpdateCategoryProcedure = "/api.taxonomy.v1.CategoryService/UpdateCategory"
	// CategoryServiceDeleteCategoryProcedure is the fully-qualified name of the CategoryService's
	// DeleteCategory RPC.
	CategoryServiceDeleteCategoryProcedure = "/api.taxonomy.v1.CategoryService/DeleteCategory"
)

// CategoryServiceClient is a client for the api.taxonomy.v1.CategoryService service.
type CategoryServiceClient interface {
	AddCategory(context.Context, *connect.Request[v1.AddCategoryRequest]) (*connect.Response[v1.AddCategoryResponse], error)
	GetAllCategories(context.Context, *connect.Request[v1.GetAllCategoriesRequest]) (*connect.Response[v1.GetAllCategoriesResponse], error)
	GetCategory(context.Context, *connect.Request[v1.GetCategoryRequest]) (*connect.Response[v1.GetCategoryResponse], error)
	UpdateCategory(context.Context, *connect.Request[v1.UpdateCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
	DeleteCategory(context.Context, *connect.Request[v1.DeleteCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewCategoryServiceClient constructs a client for the api.taxonomy.v1.CategoryService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCategoryServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CategoryServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	categoryServiceMethods := v1.File_taxonomy_v1_categories_proto.Services().ByName("CategoryService").Methods()
	return &categoryServiceClient{
		addCategory: connect.NewClient[v1.AddCategoryRequest, v1.AddCategoryResponse](
			httpClient,
			baseURL+CategoryServiceAddCategoryProcedure,
			connect.WithSchema(categoryServiceMethods.ByName("AddCategory")),
			connect.WithClientOptions(opts...),
		),
		getAllCategories: connect.NewClient[v1.GetAllCategoriesRequest, v1.GetAllCategoriesResponse](
			httpClient,
			baseURL+CategoryServiceGetAllCategoriesProcedure,
			connect.WithSchema(categoryServiceMethods.ByName("GetAllCategories")),
			connect.WithClientOptions(opts...),
		),
		getCategory: connect.NewClient[v1.GetCategoryRequest, v1.GetCategoryResponse](
			httpClient,
			baseURL+CategoryServiceGetCategoryProcedure,
			connect.WithSchema(categoryServiceMethods.ByName("GetCategory")),
			connect.WithClientOptions(opts...),
		),
		updateCategory: connect.NewClient[v1.UpdateCategoryRequest, v11.GenericResponse](
			httpClient,
			baseURL+CategoryServiceUpdateCategoryProcedure,
			connect.WithSchema(categoryServiceMethods.ByName("UpdateCategory")),
			connect.WithClientOptions(opts...),
		),
		deleteCategory: connect.NewClient[v1.DeleteCategoryRequest, v11.GenericResponse](
			httpClient,
			baseURL+CategoryServiceDeleteCategoryProcedure,
			connect.WithSchema(categoryServiceMethods.ByName("DeleteCategory")),
			connect.WithClientOptions(opts...),
		),
	}
}

// categoryServiceClient implements CategoryServiceClient.
type categoryServiceClient struct {
	addCategory      *connect.Client[v1.AddCategoryRequest, v1.AddCategoryResponse]
	getAllCategories *connect.Client[v1.GetAllCategoriesRequest, v1.GetAllCategoriesResponse]
	getCategory      *connect.Client[v1.GetCategoryRequest, v1.GetCategoryResponse]
	updateCategory   *connect.Client[v1.UpdateCategoryRequest, v11.GenericResponse]
	deleteCategory   *connect.Client[v1.DeleteCategoryRequest, v11.GenericResponse]
}

// AddCategory calls api.taxonomy.v1.CategoryService.AddCategory.
func (c *categoryServiceClient) AddCategory(ctx context.Context, req *connect.Request[v1.AddCategoryRequest]) (*connect.Response[v1.AddCategoryResponse], error) {
	return c.addCategory.CallUnary(ctx, req)
}

// GetAllCategories calls api.taxonomy.v1.CategoryService.GetAllCategories.
func (c *categoryServiceClient) GetAllCategories(ctx context.Context, req *connect.Request[v1.GetAllCategoriesRequest]) (*connect.Response[v1.GetAllCategoriesResponse], error) {
	return c.getAllCategories.CallUnary(ctx, req)
}

// GetCategory calls api.taxonomy.v1.CategoryService.GetCategory.
func (c *categoryServiceClient) GetCategory(ctx context.Context, req *connect.Request[v1.GetCategoryRequest]) (*connect.Response[v1.GetCategoryResponse], error) {
	return c.getCategory.CallUnary(ctx, req)
}

// UpdateCategory calls api.taxonomy.v1.CategoryService.UpdateCategory.
func (c *categoryServiceClient) UpdateCategory(ctx context.Context, req *connect.Request[v1.UpdateCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.updateCategory.CallUnary(ctx, req)
}

// DeleteCategory calls api.taxonomy.v1.CategoryService.DeleteCategory.
func (c *categoryServiceClient) DeleteCategory(ctx context.Context, req *connect.Request[v1.DeleteCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return c.deleteCategory.CallUnary(ctx, req)
}

// CategoryServiceHandler is an implementation of the api.taxonomy.v1.CategoryService service.
type CategoryServiceHandler interface {
	AddCategory(context.Context, *connect.Request[v1.AddCategoryRequest]) (*connect.Response[v1.AddCategoryResponse], error)
	GetAllCategories(context.Context, *connect.Request[v1.GetAllCategoriesRequest]) (*connect.Response[v1.GetAllCategoriesResponse], error)
	GetCategory(context.Context, *connect.Request[v1.GetCategoryRequest]) (*connect.Response[v1.GetCategoryResponse], error)
	UpdateCategory(context.Context, *connect.Request[v1.UpdateCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
	DeleteCategory(context.Context, *connect.Request[v1.DeleteCategoryRequest]) (*connect.Response[v11.GenericResponse], error)
}

// NewCategoryServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCategoryServiceHandler(svc CategoryServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	categoryServiceMethods := v1.File_taxonomy_v1_categories_proto.Services().ByName("CategoryService").Methods()
	categoryServiceAddCategoryHandler := connect.NewUnaryHandler(
		CategoryServiceAddCategoryProcedure,
		svc.AddCategory,
		connect.WithSchema(categoryServiceMethods.ByName("AddCategory")),
		connect.WithHandlerOptions(opts...),
	)
	categoryServiceGetAllCategoriesHandler := connect.NewUnaryHandler(
		CategoryServiceGetAllCategoriesProcedure,
		svc.GetAllCategories,
		connect.WithSchema(categoryServiceMethods.ByName("GetAllCategories")),
		connect.WithHandlerOptions(opts...),
	)
	categoryServiceGetCategoryHandler := connect.NewUnaryHandler(
		CategoryServiceGetCategoryProcedure,
		svc.GetCategory,
		connect.WithSchema(categoryServiceMethods.ByName("GetCategory")),
		connect.WithHandlerOptions(opts...),
	)
	categoryServiceUpdateCategoryHandler := connect.NewUnaryHandler(
		CategoryServiceUpdateCategoryProcedure,
		svc.UpdateCategory,
		connect.WithSchema(categoryServiceMethods.ByName("UpdateCategory")),
		connect.WithHandlerOptions(opts...),
	)
	categoryServiceDeleteCategoryHandler := connect.NewUnaryHandler(
		CategoryServiceDeleteCategoryProcedure,
		svc.DeleteCategory,
		connect.WithSchema(categoryServiceMethods.ByName("DeleteCategory")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.taxonomy.v1.CategoryService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CategoryServiceAddCategoryProcedure:
			categoryServiceAddCategoryHandler.ServeHTTP(w, r)
		case CategoryServiceGetAllCategoriesProcedure:
			categoryServiceGetAllCategoriesHandler.ServeHTTP(w, r)
		case CategoryServiceGetCategoryProcedure:
			categoryServiceGetCategoryHandler.ServeHTTP(w, r)
		case CategoryServiceUpdateCategoryProcedure:
			categoryServiceUpdateCategoryHandler.ServeHTTP(w, r)
		case CategoryServiceDeleteCategoryProcedure:
			categoryServiceDeleteCategoryHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCategoryServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCategoryServiceHandler struct{}

func (UnimplementedCategoryServiceHandler) AddCategory(context.Context, *connect.Request[v1.AddCategoryRequest]) (*connect.Response[v1.AddCategoryResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.CategoryService.AddCategory is not implemented"))
}

func (UnimplementedCategoryServiceHandler) GetAllCategories(context.Context, *connect.Request[v1.GetAllCategoriesRequest]) (*connect.Response[v1.GetAllCategoriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.CategoryService.GetAllCategories is not implemented"))
}

func (UnimplementedCategoryServiceHandler) GetCategory(context.Context, *connect.Request[v1.GetCategoryRequest]) (*connect.Response[v1.GetCategoryResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.CategoryService.GetCategory is not implemented"))
}

func (UnimplementedCategoryServiceHandler) UpdateCategory(context.Context, *connect.Request[v1.UpdateCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.CategoryService.UpdateCategory is not implemented"))
}

func (UnimplementedCategoryServiceHandler) DeleteCategory(context.Context, *connect.Request[v1.DeleteCategoryRequest]) (*connect.Response[v11.GenericResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.taxonomy.v1.CategoryService.DeleteCategory is not implemented"))
}
