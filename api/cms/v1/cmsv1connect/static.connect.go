// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: cms/v1/static.proto

package cmsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/cms/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// StaticServiceName is the fully-qualified name of the StaticService service.
	StaticServiceName = "api.cms.v1.StaticService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// StaticServiceUpdateStaticResourceProcedure is the fully-qualified name of the StaticService's
	// UpdateStaticResource RPC.
	StaticServiceUpdateStaticResourceProcedure = "/api.cms.v1.StaticService/UpdateStaticResource"
	// StaticServiceGetAllStaticResourceProcedure is the fully-qualified name of the StaticService's
	// GetAllStaticResource RPC.
	StaticServiceGetAllStaticResourceProcedure = "/api.cms.v1.StaticService/GetAllStaticResource"
	// StaticServiceAddStaticResourceProcedure is the fully-qualified name of the StaticService's
	// AddStaticResource RPC.
	StaticServiceAddStaticResourceProcedure = "/api.cms.v1.StaticService/AddStaticResource"
	// StaticServiceDeleteStaticResourceProcedure is the fully-qualified name of the StaticService's
	// DeleteStaticResource RPC.
	StaticServiceDeleteStaticResourceProcedure = "/api.cms.v1.StaticService/DeleteStaticResource"
)

// StaticServiceClient is a client for the api.cms.v1.StaticService service.
type StaticServiceClient interface {
	UpdateStaticResource(context.Context, *connect.Request[v1.UpdateStaticRequest]) (*connect.Response[v1.StaticResponse], error)
	GetAllStaticResource(context.Context, *connect.Request[v1.GetAllStaticRequest]) (*connect.Response[v1.GetAllStaticResponse], error)
	AddStaticResource(context.Context, *connect.Request[v1.AddStaticRequest]) (*connect.Response[v1.StaticResponse], error)
	DeleteStaticResource(context.Context, *connect.Request[v1.DeleteStaticRequest]) (*connect.Response[v1.DeleteStaticResponse], error)
}

// NewStaticServiceClient constructs a client for the api.cms.v1.StaticService service. By default,
// it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and
// sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC()
// or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewStaticServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) StaticServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	staticServiceMethods := v1.File_cms_v1_static_proto.Services().ByName("StaticService").Methods()
	return &staticServiceClient{
		updateStaticResource: connect.NewClient[v1.UpdateStaticRequest, v1.StaticResponse](
			httpClient,
			baseURL+StaticServiceUpdateStaticResourceProcedure,
			connect.WithSchema(staticServiceMethods.ByName("UpdateStaticResource")),
			connect.WithClientOptions(opts...),
		),
		getAllStaticResource: connect.NewClient[v1.GetAllStaticRequest, v1.GetAllStaticResponse](
			httpClient,
			baseURL+StaticServiceGetAllStaticResourceProcedure,
			connect.WithSchema(staticServiceMethods.ByName("GetAllStaticResource")),
			connect.WithClientOptions(opts...),
		),
		addStaticResource: connect.NewClient[v1.AddStaticRequest, v1.StaticResponse](
			httpClient,
			baseURL+StaticServiceAddStaticResourceProcedure,
			connect.WithSchema(staticServiceMethods.ByName("AddStaticResource")),
			connect.WithClientOptions(opts...),
		),
		deleteStaticResource: connect.NewClient[v1.DeleteStaticRequest, v1.DeleteStaticResponse](
			httpClient,
			baseURL+StaticServiceDeleteStaticResourceProcedure,
			connect.WithSchema(staticServiceMethods.ByName("DeleteStaticResource")),
			connect.WithClientOptions(opts...),
		),
	}
}

// staticServiceClient implements StaticServiceClient.
type staticServiceClient struct {
	updateStaticResource *connect.Client[v1.UpdateStaticRequest, v1.StaticResponse]
	getAllStaticResource *connect.Client[v1.GetAllStaticRequest, v1.GetAllStaticResponse]
	addStaticResource    *connect.Client[v1.AddStaticRequest, v1.StaticResponse]
	deleteStaticResource *connect.Client[v1.DeleteStaticRequest, v1.DeleteStaticResponse]
}

// UpdateStaticResource calls api.cms.v1.StaticService.UpdateStaticResource.
func (c *staticServiceClient) UpdateStaticResource(ctx context.Context, req *connect.Request[v1.UpdateStaticRequest]) (*connect.Response[v1.StaticResponse], error) {
	return c.updateStaticResource.CallUnary(ctx, req)
}

// GetAllStaticResource calls api.cms.v1.StaticService.GetAllStaticResource.
func (c *staticServiceClient) GetAllStaticResource(ctx context.Context, req *connect.Request[v1.GetAllStaticRequest]) (*connect.Response[v1.GetAllStaticResponse], error) {
	return c.getAllStaticResource.CallUnary(ctx, req)
}

// AddStaticResource calls api.cms.v1.StaticService.AddStaticResource.
func (c *staticServiceClient) AddStaticResource(ctx context.Context, req *connect.Request[v1.AddStaticRequest]) (*connect.Response[v1.StaticResponse], error) {
	return c.addStaticResource.CallUnary(ctx, req)
}

// DeleteStaticResource calls api.cms.v1.StaticService.DeleteStaticResource.
func (c *staticServiceClient) DeleteStaticResource(ctx context.Context, req *connect.Request[v1.DeleteStaticRequest]) (*connect.Response[v1.DeleteStaticResponse], error) {
	return c.deleteStaticResource.CallUnary(ctx, req)
}

// StaticServiceHandler is an implementation of the api.cms.v1.StaticService service.
type StaticServiceHandler interface {
	UpdateStaticResource(context.Context, *connect.Request[v1.UpdateStaticRequest]) (*connect.Response[v1.StaticResponse], error)
	GetAllStaticResource(context.Context, *connect.Request[v1.GetAllStaticRequest]) (*connect.Response[v1.GetAllStaticResponse], error)
	AddStaticResource(context.Context, *connect.Request[v1.AddStaticRequest]) (*connect.Response[v1.StaticResponse], error)
	DeleteStaticResource(context.Context, *connect.Request[v1.DeleteStaticRequest]) (*connect.Response[v1.DeleteStaticResponse], error)
}

// NewStaticServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewStaticServiceHandler(svc StaticServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	staticServiceMethods := v1.File_cms_v1_static_proto.Services().ByName("StaticService").Methods()
	staticServiceUpdateStaticResourceHandler := connect.NewUnaryHandler(
		StaticServiceUpdateStaticResourceProcedure,
		svc.UpdateStaticResource,
		connect.WithSchema(staticServiceMethods.ByName("UpdateStaticResource")),
		connect.WithHandlerOptions(opts...),
	)
	staticServiceGetAllStaticResourceHandler := connect.NewUnaryHandler(
		StaticServiceGetAllStaticResourceProcedure,
		svc.GetAllStaticResource,
		connect.WithSchema(staticServiceMethods.ByName("GetAllStaticResource")),
		connect.WithHandlerOptions(opts...),
	)
	staticServiceAddStaticResourceHandler := connect.NewUnaryHandler(
		StaticServiceAddStaticResourceProcedure,
		svc.AddStaticResource,
		connect.WithSchema(staticServiceMethods.ByName("AddStaticResource")),
		connect.WithHandlerOptions(opts...),
	)
	staticServiceDeleteStaticResourceHandler := connect.NewUnaryHandler(
		StaticServiceDeleteStaticResourceProcedure,
		svc.DeleteStaticResource,
		connect.WithSchema(staticServiceMethods.ByName("DeleteStaticResource")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.cms.v1.StaticService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case StaticServiceUpdateStaticResourceProcedure:
			staticServiceUpdateStaticResourceHandler.ServeHTTP(w, r)
		case StaticServiceGetAllStaticResourceProcedure:
			staticServiceGetAllStaticResourceHandler.ServeHTTP(w, r)
		case StaticServiceAddStaticResourceProcedure:
			staticServiceAddStaticResourceHandler.ServeHTTP(w, r)
		case StaticServiceDeleteStaticResourceProcedure:
			staticServiceDeleteStaticResourceHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedStaticServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedStaticServiceHandler struct{}

func (UnimplementedStaticServiceHandler) UpdateStaticResource(context.Context, *connect.Request[v1.UpdateStaticRequest]) (*connect.Response[v1.StaticResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.StaticService.UpdateStaticResource is not implemented"))
}

func (UnimplementedStaticServiceHandler) GetAllStaticResource(context.Context, *connect.Request[v1.GetAllStaticRequest]) (*connect.Response[v1.GetAllStaticResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.StaticService.GetAllStaticResource is not implemented"))
}

func (UnimplementedStaticServiceHandler) AddStaticResource(context.Context, *connect.Request[v1.AddStaticRequest]) (*connect.Response[v1.StaticResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.StaticService.AddStaticResource is not implemented"))
}

func (UnimplementedStaticServiceHandler) DeleteStaticResource(context.Context, *connect.Request[v1.DeleteStaticRequest]) (*connect.Response[v1.DeleteStaticResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.cms.v1.StaticService.DeleteStaticResource is not implemented"))
}
