package events

import (
	"context"
	"errors"
	"strconv"

	categoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/categories"
	eventsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/events"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/validation"

	"connectrpc.com/connect"
	eventsv1 "github.com/nsp-inc/vtuber/api/events/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type EventService struct {
	repo           *eventsqueries.Queries
	categoryRepo   *categoriesqueries.Queries
	storageService *storagev1.StorageService
}

func NewEventService(eventRepo *eventsqueries.Queries, categoryRepo *categoriesqueries.Queries, storageService *storagev1.StorageService) *EventService {
	return &EventService{
		repo:           eventRepo,
		categoryRepo:   categoryRepo,
		storageService: storageService,
	}
}

func (e EventService) ApproveOrRejectEvent(ctx context.Context, c *connect.Request[eventsv1.ApproveOrRejectEventRequest]) (*connect.Response[eventsv1.ApproveOrRejectEventResponse], error) {
	_, err := e.repo.GetOneEvent(ctx, strconv.FormatInt(c.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "event", nil),
		}))
	}

	status := eventsqueries.EventStatusApproved
	if c.Msg.Status == "rejected" {
		status = eventsqueries.EventStatusRejected
	}
	err = e.repo.ApproveOrRejectEventById(ctx, eventsqueries.ApproveOrRejectEventByIdParams{
		ID:     c.Msg.Id,
		Status: status,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&eventsv1.ApproveOrRejectEventResponse{
		Message: web.GetTranslation(ctx, "eventStatusUpdated", nil),
		Success: true,
	}), nil
}

func (e EventService) AddEvent(ctx context.Context, c *connect.Request[eventsv1.AddEventRequest]) (*connect.Response[eventsv1.AddEventResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	var vtuberId *int64
	status := eventsqueries.EventStatusPending
	if sessionUser.Role == "admin" {
		status = eventsqueries.EventStatusApproved
	}
	if c.Msg.CategoryId != nil {
		_, err := e.categoryRepo.GetCategoryById(ctx, strconv.FormatInt(*c.Msg.CategoryId, 10))
		if err != nil {
			return nil, errors.New(
				web.GetTranslation(ctx, "fieldNotFound", map[string]string{
					"field": web.GetTranslation(ctx, "category", nil),
				}),
			)
		}
	}
	if c.Msg.AsVtuber != nil && *c.Msg.AsVtuber {
		vtuberId = sessionUser.VtuberId
	}

	newImage, err := e.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.Image, storagev1.ImageFileType, "events", true, nil)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	slug := helpers.Slugify(c.Msg.Title)
	_, err = e.repo.GetEventBySlug(ctx, slug)
	if err == nil {
		slug = helpers.SlugifyWithTimestamp(c.Msg.Title)
	}

	event, err := e.repo.AddEvent(ctx, eventsqueries.AddEventParams{
		CategoryID:        c.Msg.CategoryId,
		Description:       "",
		ShortDescription:  c.Msg.ShortDescription,
		StartDate:         c.Msg.StartDate.AsTime(),
		EndDate:           c.Msg.EndDate.AsTime(),
		Image:             newImage,
		Rules:             c.Msg.Rules,
		Title:             c.Msg.Title,
		UserID:            sessionUser.ID,
		Status:            status,
		VtuberProfileID:   vtuberId,
		ParticipationFlow: helpers.SanitizeHtml(c.Msg.ParticipationFlow),
		Benefits:          helpers.SanitizeHtml(c.Msg.Benefits),
		Requirements:      helpers.SanitizeHtml(c.Msg.Requirements),
		Overview:          helpers.SanitizeHtml(c.Msg.Overview),
		SocialMediaLinks:  c.Msg.SocialMediaLinks.ToBytes(),
		Slug:              slug,
	})
	if err != nil {
		return nil, err
	}

	var user, vtuber *sharedv1.Profile
	if event.Userid != nil {
		user = &sharedv1.Profile{
			Id:    *event.Userid,
			Name:  *event.Username,
			Image: helpers.GetCdnLinkPointer(event.UserImage),
		}
	}

	if event.Vtuberid != nil {
		vtuber = &sharedv1.Profile{
			Id:    *event.Vtuberid,
			Name:  *event.Vtubername,
			Image: helpers.GetCdnLinkPointer(event.Vtuberimage),
		}
	}

	return connect.NewResponse(&eventsv1.AddEventResponse{
		Data: &eventsv1.Event{
			Id:                event.ID,
			CategoryId:        event.CategoryID,
			User:              user,
			Vtuber:            vtuber,
			Description:       event.Description,
			ShortDescription:  event.ShortDescription,
			StartDate:         timestamppb.New(event.StartDate),
			EndDate:           timestamppb.New(event.EndDate),
			Image:             helpers.GetCdnUrl(event.Image),
			Rules:             event.Rules,
			Title:             event.Title,
			Status:            string(event.Status),
			CreatedAt:         timestamppb.New(event.CreatedAt),
			ParticipationFlow: event.ParticipationFlow,
			Benefits:          event.Benefits,
			Requirements:      event.Requirements,
			Overview:          event.Overview,
			SocialMediaLinks:  sharedv1.SocialMediaLinksFromBytes(event.SocialMediaLinks),
			Slug:              event.Slug,
		},
	}), nil
}

func (e EventService) GetAllEvents(ctx context.Context, c *connect.Request[eventsv1.GetAllEventsRequest]) (*connect.Response[eventsv1.GetAllEventsResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	isAdmin := sessionUser != nil && sessionUser.Role == "admin"
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"id", "title", "created_at", "updated_at"})
	events, err := e.repo.GetAllEvents(ctx, eventsqueries.GetAllEventsParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		IsAdmin:  isAdmin,
		VtuberID: c.Msg.VtuberId,
	})
	if err != nil {
		return nil, err
	}

	var eventsData []*eventsv1.Event
	for _, event := range events {
		var user, vtuber *sharedv1.Profile
		if event.Userid != nil {
			user = &sharedv1.Profile{
				Id:    *event.Userid,
				Name:  *event.Username,
				Image: event.UserImage,
			}
		}

		if event.Vtuberid != nil {
			vtuber = &sharedv1.Profile{
				Id:    *event.Vtuberid,
				Name:  *event.Vtubername,
				Image: event.Vtuberimage,
			}
		}

		eventsData = append(eventsData, &eventsv1.Event{
			Id:                event.ID,
			CategoryId:        event.CategoryID,
			User:              user,
			Vtuber:            vtuber,
			Description:       event.Description,
			ShortDescription:  event.ShortDescription,
			StartDate:         timestamppb.New(event.StartDate),
			EndDate:           timestamppb.New(event.EndDate),
			Image:             helpers.GetCdnUrl(event.Image),
			Rules:             event.Rules,
			Title:             event.Title,
			Status:            string(event.Status),
			CreatedAt:         timestamppb.New(event.CreatedAt),
			ParticipationFlow: event.ParticipationFlow,
			Benefits:          event.Benefits,
			Requirements:      event.Requirements,
			Overview:          event.Overview,
			SocialMediaLinks:  sharedv1.SocialMediaLinksFromBytes(event.SocialMediaLinks),
			Slug:              event.Slug,
		})
	}

	return connect.NewResponse(&eventsv1.GetAllEventsResponse{
		Data:              eventsData,
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(events)),
	}), nil

}

func (e EventService) GetMyEvents(ctx context.Context, c *connect.Request[eventsv1.GetAllEventsRequest]) (*connect.Response[eventsv1.GetAllEventsResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	var vtuber = int64(0)
	if sessionUser != nil {
		vtuber = *sessionUser.VtuberId
	}
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"id", "title", "created_at", "updated_at"})
	events, err := e.repo.GetMyEvents(ctx, eventsqueries.GetMyEventsParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		VtuberID: vtuber,
	})
	if err != nil {
		return nil, err
	}

	var eventsData []*eventsv1.Event
	for _, event := range events {
		var user, vtuber *sharedv1.Profile
		if event.Userid != nil {
			user = &sharedv1.Profile{
				Id:    *event.Userid,
				Name:  *event.Username,
				Image: event.UserImage,
			}
		}

		if event.Vtuberid != nil {
			vtuber = &sharedv1.Profile{
				Id:    *event.Vtuberid,
				Name:  *event.Vtubername,
				Image: event.Vtuberimage,
			}
		}

		eventsData = append(eventsData, &eventsv1.Event{
			Id:                event.ID,
			CategoryId:        event.CategoryID,
			User:              user,
			Vtuber:            vtuber,
			Description:       event.Description,
			ShortDescription:  event.ShortDescription,
			StartDate:         timestamppb.New(event.StartDate),
			EndDate:           timestamppb.New(event.EndDate),
			Image:             helpers.GetCdnUrl(event.Image),
			Rules:             event.Rules,
			Title:             event.Title,
			Status:            string(event.Status),
			CreatedAt:         timestamppb.New(event.CreatedAt),
			ParticipationFlow: event.ParticipationFlow,
			Benefits:          event.Benefits,
			Requirements:      event.Requirements,
			Overview:          event.Overview,
			SocialMediaLinks:  sharedv1.SocialMediaLinksFromBytes(event.SocialMediaLinks),
			Slug:              event.Slug,
		})
	}

	return connect.NewResponse(&eventsv1.GetAllEventsResponse{
		Data:              eventsData,
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(events)),
	}), nil
}

func (e EventService) GetEventById(ctx context.Context, c *connect.Request[eventsv1.GetEventByIdRequest]) (*connect.Response[eventsv1.GetEventByIdResponse], error) {
	event, err := e.repo.GetEventById(ctx,
		c.Msg.Id,
	)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "event", nil),
		}))
	}

	var user, vtuber *sharedv1.Profile
	if event.UserID != nil {
		user = &sharedv1.Profile{
			Id:    *event.UserID,
			Name:  *event.Username,
			Image: event.UserImage,
		}
	}

	if event.Vtuberid != nil {
		vtuber = &sharedv1.Profile{
			Id:    *event.Vtuberid,
			Name:  *event.Vtubername,
			Image: event.Vtuberimage,
		}
	}

	return connect.NewResponse(&eventsv1.GetEventByIdResponse{
		Data: &eventsv1.Event{
			Id:                event.ID,
			CategoryId:        event.CategoryID,
			User:              user,
			Vtuber:            vtuber,
			Description:       event.Description,
			ShortDescription:  event.ShortDescription,
			StartDate:         timestamppb.New(event.StartDate),
			EndDate:           timestamppb.New(event.EndDate),
			Image:             helpers.GetCdnUrl(event.Image),
			Rules:             event.Rules,
			Title:             event.Title,
			Status:            string(event.Status),
			CreatedAt:         timestamppb.New(event.CreatedAt),
			ParticipationFlow: event.ParticipationFlow,
			Benefits:          event.Benefits,
			Requirements:      event.Requirements,
			Overview:          event.Overview,
			SocialMediaLinks:  sharedv1.SocialMediaLinksFromBytes(event.SocialMediaLinks),
			Slug:              event.Slug,
		},
	}), nil

}

func (e EventService) DeleteEventById(ctx context.Context, c *connect.Request[eventsv1.DeleteEventByIdRequest]) (*connect.Response[eventsv1.DeleteEventByIdResponse], error) {
	err := e.repo.DeleteEventById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "event", nil),
			}),
		)
	}
	return connect.NewResponse(&eventsv1.DeleteEventByIdResponse{
		Success: true,
		Message: web.GetTranslation(ctx, "eventDeleted", nil),
	}), nil
}

func (e EventService) UpdateEventById(ctx context.Context, c *connect.Request[eventsv1.UpdateEventByIdRequest]) (*connect.Response[eventsv1.UpdateEventByIdResponse], error) {
	event, err := e.repo.GetOneEvent(ctx, strconv.FormatInt(c.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "event", nil),
			}),
		)
	}
	if c.Msg.CategoryId != nil {
		_, err = e.categoryRepo.GetCategoryById(ctx, strconv.FormatInt(*c.Msg.CategoryId, 10))
		if err != nil {
			return nil, errors.New(
				web.GetTranslation(ctx, "fieldNotFound", map[string]string{
					"field": web.GetTranslation(ctx, "category", nil),
				}),
			)
		}
	}
	sessionUser := web.GetUserFromContext(ctx)
	if !sessionUser.IsAdmin() && event.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}

	newImage, err := e.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.Image, storagev1.ImageFileType, "events", false, &event.Image)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	err = e.repo.UpdateEventById(ctx, eventsqueries.UpdateEventByIdParams{
		ID:                c.Msg.Id,
		CategoryID:        c.Msg.CategoryId,
		Description:       "",
		StartDate:         c.Msg.StartDate.AsTime(),
		EndDate:           c.Msg.EndDate.AsTime(),
		Image:             newImage,
		Rules:             c.Msg.Rules,
		Title:             c.Msg.Title,
		ShortDescription:  c.Msg.ShortDescription,
		ParticipationFlow: helpers.SanitizeHtml(c.Msg.ParticipationFlow),
		Benefits:          helpers.SanitizeHtml(c.Msg.Benefits),
		Requirements:      helpers.SanitizeHtml(c.Msg.Requirements),
		Overview:          helpers.SanitizeHtml(c.Msg.Overview),
		SocialMediaLinks:  c.Msg.GetSocialMediaLinks().ToBytes(),
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&eventsv1.UpdateEventByIdResponse{
		Message: web.GetTranslation(ctx, "eventUpdated", nil),
		Success: true,
	}), nil
}

func (e *EventService) GetAllEventsByCategory(ctx context.Context, c *connect.Request[eventsv1.GetAllEventsByCategoryRequest]) (*connect.Response[eventsv1.GetAllEventsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"title", "created_at", "updated_at"})
	events, err := e.repo.GetAllEvents(ctx, eventsqueries.GetAllEventsParams{
		Limit:      paginationInfo.PageSize,
		Offset:     paginationInfo.Offset,
		Order:      paginationInfo.OrderDirection,
		Sort:       paginationInfo.OrderBy,
		CategoryID: &c.Msg.CategoryId,
	})
	if err != nil {
		return nil, err
	}

	var eventsData []*eventsv1.Event
	for _, event := range events {
		var user, vtuber *sharedv1.Profile
		if event.Userid != nil {
			user = &sharedv1.Profile{
				Id:    *event.Userid,
				Name:  *event.Username,
				Image: event.UserImage,
			}
		}

		if event.Vtuberid != nil {
			vtuber = &sharedv1.Profile{
				Id:    *event.Vtuberid,
				Name:  *event.Vtubername,
				Image: event.Vtuberimage,
			}
		}

		eventsData = append(eventsData, &eventsv1.Event{
			Id:                event.ID,
			CategoryId:        event.CategoryID,
			User:              user,
			Vtuber:            vtuber,
			Description:       event.Description,
			ShortDescription:  event.ShortDescription,
			StartDate:         timestamppb.New(event.StartDate),
			EndDate:           timestamppb.New(event.EndDate),
			Image:             helpers.GetCdnUrl(event.Image),
			Rules:             event.Rules,
			Title:             event.Title,
			Status:            string(event.Status),
			CreatedAt:         timestamppb.New(event.CreatedAt),
			ParticipationFlow: event.ParticipationFlow,
			Benefits:          event.Benefits,
			Requirements:      event.Requirements,
			Overview:          event.Overview,
			SocialMediaLinks:  sharedv1.SocialMediaLinksFromBytes(event.SocialMediaLinks),
			Slug:              event.Slug,
		})
	}

	return connect.NewResponse(&eventsv1.GetAllEventsResponse{
		Data:              eventsData,
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(events)),
	}), nil
}

func (e EventService) GetUserEvents(ctx context.Context, req *connect.Request[eventsv1.GetAllEventsRequest]) (*connect.Response[eventsv1.GetAllEventsResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at", "updated_at"})

	events, err := e.repo.GetUserEvents(ctx, eventsqueries.GetUserEventsParams{
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
		UserID: sessionUser.ID,
	})
	if err != nil {
		return nil, err
	}
	var eventsData []*eventsv1.Event
	for _, event := range events {
		user := &sharedv1.Profile{
			Id:    event.Userid,
			Name:  event.Username,
			Image: event.UserImage,
		}

		eventsData = append(eventsData, &eventsv1.Event{
			Id:                event.ID,
			CategoryId:        event.CategoryID,
			User:              user,
			Description:       event.Description,
			ShortDescription:  event.ShortDescription,
			StartDate:         timestamppb.New(event.StartDate),
			EndDate:           timestamppb.New(event.EndDate),
			Image:             helpers.GetCdnUrl(event.Image),
			Rules:             event.Rules,
			Title:             event.Title,
			Status:            string(event.Status),
			CreatedAt:         timestamppb.New(event.CreatedAt),
			ParticipationFlow: event.ParticipationFlow,
			Benefits:          event.Benefits,
			Requirements:      event.Requirements,
			Overview:          event.Overview,
			SocialMediaLinks:  sharedv1.SocialMediaLinksFromBytes(event.SocialMediaLinks),
			Slug:              event.Slug,
		})
	}

	return connect.NewResponse(&eventsv1.GetAllEventsResponse{
		Data:              eventsData,
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(events)),
	}), nil
}
