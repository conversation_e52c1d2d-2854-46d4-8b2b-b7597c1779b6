package cms

import (
	"context"
	"errors"

	"connectrpc.com/connect"
	cmsv1 "github.com/nsp-inc/vtuber/api/cms/v1"
	staticdataqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/staticdata"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type StaticService struct {
	repo *staticdataqueries.Queries
}

func NewStaticService(repo *staticdataqueries.Queries) *StaticService {
	return &StaticService{
		repo: repo,
	}
}

func (s *StaticService) GetAllStaticResource(ctx context.Context, req *connect.Request[cmsv1.GetAllStaticRequest]) (*connect.Response[cmsv1.GetAllStaticResponse], error) {
	static, err := s.repo.GetAllStaticResource(ctx, req.Msg.Key)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&cmsv1.GetAllStaticResponse{
		Data: helpers.Map(static, func(static staticdataqueries.StaticDatum) *cmsv1.StaticResponse {
			return &cmsv1.StaticResponse{
				Id:        static.ID,
				Key:       static.Key,
				Value:     static.Value,
				Language:  string(static.Language),
				CreatedAt: timestamppb.New(static.CreatedAt),
				UpdatedAt: timestamppb.New(static.UpdatedAt),
			}
		}),
	}), nil
}

func (s *StaticService) UpdateStaticResource(ctx context.Context, req *connect.Request[cmsv1.UpdateStaticRequest]) (*connect.Response[cmsv1.StaticResponse], error) {

	_, err := s.repo.GetStaticResourceById(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}

	static, err := s.repo.UpdateStaticResource(ctx, staticdataqueries.UpdateStaticResourceParams{
		ID:    req.Msg.Id,
		Value: req.Msg.Value,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&cmsv1.StaticResponse{
		Id:        static.ID,
		Key:       static.Key,
		Value:     static.Value,
		Language:  string(static.Language),
		CreatedAt: timestamppb.New(static.CreatedAt),
		UpdatedAt: timestamppb.New(static.UpdatedAt),
	}), nil
}

func (s *StaticService) AddStaticResource(ctx context.Context, req *connect.Request[cmsv1.AddStaticRequest]) (*connect.Response[cmsv1.StaticResponse], error) {

	_, err := s.repo.GetStaticResourceByKeyAndLanguage(ctx, staticdataqueries.GetStaticResourceByKeyAndLanguageParams{
		Key:      req.Msg.Key,
		Language: staticdataqueries.Language(req.Msg.Language),
	})

	if err == nil {
		lan := req.Msg.Language
		switch lan {
		case "en-us":
			lan = "English"
		case "ja-jp":
			lan = "Japanese"
		}
		return nil, errors.New("static resource already exists for " + lan)
	}

	static, err := s.repo.AddStaticResource(ctx, staticdataqueries.AddStaticResourceParams{
		Key:      req.Msg.Key,
		Value:    req.Msg.Value,
		Language: staticdataqueries.Language(req.Msg.Language),
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&cmsv1.StaticResponse{
		Id:        static.ID,
		Key:       static.Key,
		Value:     static.Value,
		Language:  string(static.Language),
		CreatedAt: timestamppb.New(static.CreatedAt),
		UpdatedAt: timestamppb.New(static.UpdatedAt),
	}), nil
}

func (s *StaticService) DeleteStaticResource(ctx context.Context, req *connect.Request[cmsv1.DeleteStaticRequest]) (*connect.Response[cmsv1.DeleteStaticResponse], error) {

	_, err := s.repo.GetStaticResourceById(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	err = s.repo.DeleteStaticResource(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&cmsv1.DeleteStaticResponse{
		Message: "Static resource deleted",
		Success: true,
	}), nil
}
