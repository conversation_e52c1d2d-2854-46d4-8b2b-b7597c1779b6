package campaigns

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"

	campaignsv1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	campaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigns"
	campaignvariantsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariants"
	campaignvariantsubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariantsubscriptions"
	transactionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/transactions"
	userbillinginfosqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userbillinginfos"
	userpointsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userpoints"
	"github.com/nsp-inc/vtuber/internal/domain/billing"
	"github.com/nsp-inc/vtuber/packages/gmo"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type CampaignVariantSubscriptionService struct {
	userBillingRepo                     *userbillinginfosqueries.Queries
	campaignVariantRepo                 *campaignvariantsqueries.Queries
	campaignvariantsubscriptionsqueries *campaignvariantsubscriptionsqueries.Queries
	campaignRepo                        *campaignsqueries.Queries
	userPointRepo                       *userpointsqueries.Queries
	transactionRepo                     *transactionsqueries.Queries
}

func NewCampaignVariantSubscriptionService(
	userBillingRepo *userbillinginfosqueries.Queries,
	campaignVariantRepo *campaignvariantsqueries.Queries,
	campaignvariantsubscriptionsqueries *campaignvariantsubscriptionsqueries.Queries,
	campaignRepo *campaignsqueries.Queries,
	userPointRepo *userpointsqueries.Queries,
	transactionRepo *transactionsqueries.Queries) *CampaignVariantSubscriptionService {
	return &CampaignVariantSubscriptionService{

		userBillingRepo:                     userBillingRepo,
		campaignVariantRepo:                 campaignVariantRepo,
		campaignRepo:                        campaignRepo,
		userPointRepo:                       userPointRepo,
		transactionRepo:                     transactionRepo,
		campaignvariantsubscriptionsqueries: campaignvariantsubscriptionsqueries,
	}
}

func (c CampaignVariantSubscriptionService) AddCampaignVariantSubscription(ctx context.Context, req *connect.Request[campaignsv1.AddCampaignVariantSubscriptionRequest]) (*connect.Response[campaignsv1.AddCampaignVariantSubscriptionResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	campaignVariant, err := c.campaignVariantRepo.GetOneCampaignVariantById(ctx, req.Msg.CampaignVariantId)
	if err != nil {
		return nil, err
	}
	if campaignVariant.MaxSub > 0 {
		count, err := c.campaignvariantsubscriptionsqueries.GetCampaignVariantSubscriptionCount(ctx, campaignVariant.ID)
		if err != nil {
			return nil, err
		}
		if count >= int64(campaignVariant.MaxSub) {
			return nil, errors.New(
				web.GetTranslation(ctx, "subLimitReached", nil),
			)
		}
	}
	campaignById, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(campaignVariant.CampaignID, 10))
	if err != nil {
		return nil, err
	}
	billingInfo, err := c.userBillingRepo.GetBillingInfoById(ctx, req.Msg.UserBillingInfoId)
	if err != nil {
		return nil, err
	}

	if billingInfo.UserID != sessionUser.ID {
		return nil, errors.New("invalid billing info")
	}

	_, err = c.campaignvariantsubscriptionsqueries.GetCampaignVariantSubscriptionByUserIdAndCampaignVariantId(ctx, campaignvariantsubscriptionsqueries.GetCampaignVariantSubscriptionByUserIdAndCampaignVariantIdParams{
		CampaignVariantID: req.Msg.CampaignVariantId,
		UserID:            sessionUser.ID,
	})

	if err == nil {
		return nil, errors.New("already subscribed")
	}

	//gmo call
	var gmoOrderId string
	for {
		gmoOrderId = gmo.GenerateGmoOrderId()
		_, err = c.transactionRepo.CheckGmoOrderIdExist(ctx, gmoOrderId)
		if err != nil {
			break
		}
	}
	createTransactionResponse, err := gmo.GmoAPICall(
		gmo.GmoApiParams{
			MemberID: billingInfo.GmoMemberID,
			OrderID:  gmoOrderId,
			Amount:   strconv.Itoa(int(campaignVariant.Price)),
			Status:   "AUTH",
			JobCd:    "AUTH",
		}, gmo.EntryTranEndPoint)

	if err != nil {
		return nil, err
	}

	_, err = gmo.GmoAPICall(
		gmo.GmoApiParams{
			MemberID:   billingInfo.GmoMemberID,
			OrderID:    gmoOrderId,
			AccessID:   createTransactionResponse.AccessID,
			AccessPass: createTransactionResponse.AccessPass,
			Method:     "1",
			SeqMode:    "0",
			CardSeq:    "0",
		}, gmo.ExecTranEndPoint)

	if err != nil {
		return nil, err
	}

	_, err = gmo.GmoAPICall(
		gmo.GmoApiParams{
			Amount:     strconv.Itoa(int(campaignVariant.Price)),
			AccessID:   createTransactionResponse.AccessID,
			AccessPass: createTransactionResponse.AccessPass,
			JobCd:      "SALES",
			Tax:        "0",
			Method:     "1",
			PayTimes:   "1",
		}, gmo.AlterTranEndPoint)

	if err != nil {
		return nil, err
	}
	savedSubscription, err := c.campaignvariantsubscriptionsqueries.AddCampaignVariantSubscription(ctx, campaignvariantsubscriptionsqueries.AddCampaignVariantSubscriptionParams{
		UserID:            sessionUser.ID,
		CampaignVariantID: req.Msg.CampaignVariantId,
		VtuberID:          campaignById.VtuberID,
		Price:             campaignVariant.Price,
		CampaignID:        campaignVariant.CampaignID,
		Comment:           req.Msg.Comment,
	})
	if err != nil {
		return nil, err
	}

	//save transaction
	card, err := gmo.SearchCard(ctx, billingInfo.GmoMemberID)
	if err != nil {
		return nil, err
	}

	var cardNo string
	var cardExpiry string
	if card != nil {
		cardNo = card.CardNumber
		cardExpiry = card.CardExpiryDate
	} else {
		cardNo = "N/A"
		cardExpiry = "N/A"
	}

	transactionDetail, err := billing.TransactionDetail{
		CampaignVariantSubscription: &billing.CampaignVariantSubscriptionDetails{
			CampaignVariantId: req.Msg.CampaignVariantId,
			Amount:            campaignVariant.Price,
			CampaignId:        campaignVariant.CampaignID,
			VtuberId:          campaignById.VtuberID,
		},
		BillingInfo: &billing.BillingInfo{
			CardNumber:     cardNo,
			CardExpiryDate: cardExpiry,
			FullName:       billingInfo.FullName,
			Address1:       billingInfo.Address1,
			Address2:       billingInfo.Address2,
			City:           billingInfo.City,
			State:          billingInfo.State,
			Country:        billingInfo.Country,
			PostalCode:     billingInfo.PostalCode,
			CompanyName:    billingInfo.CompanyName,
			VatNumber:      billingInfo.VatNumber,
		},
	}.ToByte()

	if err != nil {
		return nil, err
	}
	transaction, err := c.transactionRepo.AddTransaction(ctx, transactionsqueries.AddTransactionParams{
		UserID:     sessionUser.ID,
		Amount:     campaignVariant.Price,
		Type:       "transaction",
		Status:     "payment_done",
		VtuberID:   &campaignById.VtuberID,
		GmoOrderID: gmoOrderId,
		Details:    transactionDetail,
	})
	if err != nil {
		return nil, err
	}

	points := float64(campaignVariant.Price) / float64(500)
	_, err = c.userPointRepo.AddUserPoints(ctx, userpointsqueries.AddUserPointsParams{
		UserID:        sessionUser.ID,
		TransactionID: &transaction.ID,
		Points:        points,
	})
	if err != nil {
		return nil, err
	}
	//todo::send notification to vtuber
	//todo::send notification to user
	//todo::send success email to user
	return connect.NewResponse(&campaignsv1.AddCampaignVariantSubscriptionResponse{
		Data: &campaignsv1.CampaignVariantSubscription{
			UserId:            savedSubscription.UserID,
			CampaignVariantId: savedSubscription.CampaignVariantID,
			Price:             savedSubscription.Price,
			CreatedAt:         timestamppb.New(savedSubscription.CreatedAt),
			VtuberId:          savedSubscription.VtuberID,
			Comment:           savedSubscription.Comment,
		},
	}), nil
}
