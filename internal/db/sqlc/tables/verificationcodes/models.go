// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package verificationcodesqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type VerificationCodeType string

const (
	VerificationCodeTypeSignupEmailVerification VerificationCodeType = "signup_email_verification"
	VerificationCodeTypeEmailVerification       VerificationCodeType = "email_verification"
	VerificationCodeTypePasswordReset           VerificationCodeType = "password_reset"
	VerificationCodeTypeChangeEmail             VerificationCodeType = "change_email"
	VerificationCodeTypeChangeEmailRequest      VerificationCodeType = "change_email_request"
)

func (e *VerificationCodeType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = VerificationCodeType(s)
	case string:
		*e = VerificationCodeType(s)
	default:
		return fmt.Errorf("unsupported scan type for VerificationCodeType: %T", src)
	}
	return nil
}

type NullVerificationCodeType struct {
	VerificationCodeType VerificationCodeType
	Valid                bool // Valid is true if VerificationCodeType is not NULL
}

// <PERSON>an implements the Scanner interface.
func (ns *NullVerificationCodeType) Scan(value interface{}) error {
	if value == nil {
		ns.VerificationCodeType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.VerificationCodeType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullVerificationCodeType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.VerificationCodeType), nil
}

type VerificationCode struct {
	ID        int64
	UserID    *int64
	Code      string
	Email     *string
	ExpiresAt time.Time
	CreatedAt time.Time
	UpdatedAt time.Time
	Type      VerificationCodeType
}
