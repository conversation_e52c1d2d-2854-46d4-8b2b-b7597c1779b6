// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package eventsqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type EventStatus string

const (
	EventStatusApproved EventStatus = "approved"
	EventStatusRejected EventStatus = "rejected"
	EventStatusPending  EventStatus = "pending"
)

func (e *EventStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventStatus(s)
	case string:
		*e = EventStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for EventStatus: %T", src)
	}
	return nil
}

type NullEventStatus struct {
	EventStatus EventStatus
	Valid       bool // Valid is true if EventStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEventStatus) Scan(value interface{}) error {
	if value == nil {
		ns.EventStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventStatus), nil
}

type Event struct {
	ID                int64
	Slug              string
	SocialMediaLinks  []byte
	CategoryID        *int64
	UserID            int64
	VtuberProfileID   *int64
	ShortDescription  string
	Title             string
	Description       string
	Image             string
	Rules             string
	StartDate         time.Time
	EndDate           time.Time
	Status            EventStatus
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	CreatedAt         time.Time
	UpdatedAt         time.Time
	DeletedAt         *time.Time
}
