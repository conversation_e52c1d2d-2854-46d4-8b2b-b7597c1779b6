// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package vtuberprofilesqueries

import (
	"time"
)

type VtuberProfile struct {
	ID               int64
	UserID           int64
	DisplayName      string
	Username         string
	Furigana         string
	Image            *string
	BannerImage      *string
	SocialMediaLinks []byte
	Description      *string
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
}
