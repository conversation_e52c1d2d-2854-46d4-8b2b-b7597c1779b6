// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: staticdata.sql

package staticdataqueries

import (
	"context"
)

const addStaticResource = `-- name: AddStaticResource :one
INSERT INTO static_data (key, value, language) VALUES ($1, $2, $3) RETURNING id, key, value, language, created_at, updated_at
`

type AddStaticResourceParams struct {
	Key      string
	Value    string
	Language Language
}

func (q *Queries) AddStaticResource(ctx context.Context, arg AddStaticResourceParams) (StaticDatum, error) {
	row := q.db.QueryRow(ctx, addStaticResource, arg.Key, arg.Value, arg.Language)
	var i StaticDatum
	err := row.Scan(
		&i.ID,
		&i.Key,
		&i.Value,
		&i.Language,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteStaticResource = `-- name: DeleteStaticResource :exec
DELETE FROM static_data WHERE id = $1
`

func (q *Queries) DeleteStaticResource(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteStaticResource, id)
	return err
}

const getAllStaticResource = `-- name: GetAllStaticResource :many
SELECT id, key, value, language, created_at, updated_at FROM static_data WHERE key = $1
`

func (q *Queries) GetAllStaticResource(ctx context.Context, key string) ([]StaticDatum, error) {
	rows, err := q.db.Query(ctx, getAllStaticResource, key)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []StaticDatum
	for rows.Next() {
		var i StaticDatum
		if err := rows.Scan(
			&i.ID,
			&i.Key,
			&i.Value,
			&i.Language,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStaticResourceById = `-- name: GetStaticResourceById :one
SELECT id, key, value, language, created_at, updated_at FROM static_data WHERE id = $1
`

func (q *Queries) GetStaticResourceById(ctx context.Context, id int64) (StaticDatum, error) {
	row := q.db.QueryRow(ctx, getStaticResourceById, id)
	var i StaticDatum
	err := row.Scan(
		&i.ID,
		&i.Key,
		&i.Value,
		&i.Language,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStaticResourceByKeyAndLanguage = `-- name: GetStaticResourceByKeyAndLanguage :one
SELECT id, key, value, language, created_at, updated_at FROM static_data WHERE key = $1 AND language = $2
`

type GetStaticResourceByKeyAndLanguageParams struct {
	Key      string
	Language Language
}

func (q *Queries) GetStaticResourceByKeyAndLanguage(ctx context.Context, arg GetStaticResourceByKeyAndLanguageParams) (StaticDatum, error) {
	row := q.db.QueryRow(ctx, getStaticResourceByKeyAndLanguage, arg.Key, arg.Language)
	var i StaticDatum
	err := row.Scan(
		&i.ID,
		&i.Key,
		&i.Value,
		&i.Language,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateStaticResource = `-- name: UpdateStaticResource :one
UPDATE static_data SET value = $1 WHERE id = $2 RETURNING id, key, value, language, created_at, updated_at
`

type UpdateStaticResourceParams struct {
	Value string
	ID    int64
}

func (q *Queries) UpdateStaticResource(ctx context.Context, arg UpdateStaticResourceParams) (StaticDatum, error) {
	row := q.db.QueryRow(ctx, updateStaticResource, arg.Value, arg.ID)
	var i StaticDatum
	err := row.Scan(
		&i.ID,
		&i.Key,
		&i.Value,
		&i.Language,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
