// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package transactionsqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type TransactionStatus string

const (
	TransactionStatusPaymentInit      TransactionStatus = "payment_init"
	TransactionStatusPaymentWaiting   TransactionStatus = "payment_waiting"
	TransactionStatusPaymentDone      TransactionStatus = "payment_done"
	TransactionStatusPaymentCancelled TransactionStatus = "payment_cancelled"
)

func (e *TransactionStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TransactionStatus(s)
	case string:
		*e = TransactionStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for TransactionStatus: %T", src)
	}
	return nil
}

type NullTransactionStatus struct {
	TransactionStatus TransactionStatus
	Valid             bool // Valid is true if TransactionStatus is not NULL
}

// <PERSON>an implements the Scanner interface.
func (ns *NullTransactionStatus) Scan(value interface{}) error {
	if value == nil {
		ns.TransactionStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TransactionStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTransactionStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TransactionStatus), nil
}

type TransactionType string

const (
	TransactionTypeTransaction TransactionType = "transaction"
	TransactionTypeRefund      TransactionType = "refund"
)

func (e *TransactionType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TransactionType(s)
	case string:
		*e = TransactionType(s)
	default:
		return fmt.Errorf("unsupported scan type for TransactionType: %T", src)
	}
	return nil
}

type NullTransactionType struct {
	TransactionType TransactionType
	Valid           bool // Valid is true if TransactionType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullTransactionType) Scan(value interface{}) error {
	if value == nil {
		ns.TransactionType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TransactionType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTransactionType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TransactionType), nil
}

type Transaction struct {
	ID         int64
	UserID     int64
	Amount     int32
	Type       TransactionType
	Status     TransactionStatus
	Details    []byte
	GmoOrderID string
	VtuberID   *int64
	CreatedAt  time.Time
	UpdatedAt  time.Time
}
