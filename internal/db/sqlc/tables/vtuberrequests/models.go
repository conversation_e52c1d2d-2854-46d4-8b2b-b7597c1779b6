// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package vtuberrequestsqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type VtuberAccessStatus string

const (
	VtuberAccessStatusApplied  VtuberAccessStatus = "applied"
	VtuberAccessStatusPending  VtuberAccessStatus = "pending"
	VtuberAccessStatusApproved VtuberAccessStatus = "approved"
	VtuberAccessStatusRejected VtuberAccessStatus = "rejected"
)

func (e *VtuberAccessStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = VtuberAccessStatus(s)
	case string:
		*e = VtuberAccessStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for VtuberAccessStatus: %T", src)
	}
	return nil
}

type NullVtuberAccessStatus struct {
	VtuberAccessStatus VtuberAccessStatus
	Valid              bool // Valid is true if VtuberAccessStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullVtuberAccessStatus) Scan(value interface{}) error {
	if value == nil {
		ns.VtuberAccessStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.VtuberAccessStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullVtuberAccessStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.VtuberAccessStatus), nil
}

type VtuberRequest struct {
	ID          int64
	Description string
	Status      VtuberAccessStatus
	Reason      *string
	UserID      int64
	CreatedAt   time.Time
	UpdatedAt   time.Time
}
