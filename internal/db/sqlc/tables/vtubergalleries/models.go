// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package vtubergalleriesqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type MediaType string

const (
	MediaTypePicture MediaType = "picture"
	MediaTypeVideo   MediaType = "video"
	MediaTypeAudio   MediaType = "audio"
)

func (e *MediaType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = MediaType(s)
	case string:
		*e = MediaType(s)
	default:
		return fmt.Errorf("unsupported scan type for MediaType: %T", src)
	}
	return nil
}

type NullMediaType struct {
	MediaType MediaType
	Valid     bool // Valid is true if MediaType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullMediaType) Scan(value interface{}) error {
	if value == nil {
		ns.MediaType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.MediaType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullMediaType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.MediaType), nil
}

type VtuberGallery struct {
	ID          int64
	VtuberID    int64
	Media       string
	MediaType   MediaType
	Description *string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}
