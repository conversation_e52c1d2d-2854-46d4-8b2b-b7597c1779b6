-- name: CreateCampaign :one
INSERT INTO campaigns (name, description, start_date, end_date, total_budget, vtuber_id, thumbnail,
                       short_description, promotional_message, social_media_links,slug)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING *;

-- name: GetCampaignById :one
SELECT c.*,
       vp.display_name   as vtuber_name,
       vp.image          as vtuber_image,
       vp.user_id        as vtuber_user_id,
       vp.furigana       as vtuber_furigana,
       vp.banner_image   as vtuber_banner_image,
       vp.social_media_links as vtuber_social_media_links,
       vp.description    as vtuber_description,
       vp.created_at as vtuber_created_at,
         (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE campaign_id::TEXT = sqlc.arg('id')::TEXT) as total_raised,
         ARRAY_AGG(cc.category_id)::BIGINT[] as category_ids
FROM campaigns c
         INNER JOIN vtuber_profiles vp ON c.vtuber_id = vp.id
         INNER JOIN campaign_categories cc ON c.id = cc.campaign_id
WHERE (c.id::TEXT = sqlc.arg('id') OR c.slug = sqlc.arg('id')::TEXT)
  AND c.deleted_at IS NULL 
  GROUP BY c.id, vp.display_name , vp.image, vp.user_id, vp.furigana, vp.banner_image, vp.social_media_links, vp.description, vp.created_at;

-- name: GetCampaignsByVtuber :many
WITH FILTERED AS (SELECT c.*, (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE c.id = campaign_variant_subscriptions.campaign_id) as total_raised
                  FROM campaigns c
                  WHERE c.vtuber_id = $1
                    AND c.deleted_at IS NULL),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'name' AND sqlc.arg('order')::TEXT = 'ASC' THEN name
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'name' AND sqlc.arg('order')::TEXT = 'DESC' THEN name
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'total_budget' AND sqlc.arg('order')::TEXT = 'ASC' THEN total_budget
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'total_budget' AND sqlc.arg('order')::TEXT = 'DESC' THEN total_budget
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at
END
DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: GetAllCampaigns :many
WITH FILTERED AS (SELECT c.*, vp.display_name, (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE c.id = campaign_variant_subscriptions.campaign_id) as total_raised
                  FROM campaigns c 
                  INNER JOIN vtuber_profiles vp ON vp.id = c.vtuber_id  
                   WHERE c.deleted_at IS NULL),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'name' AND sqlc.arg('order')::TEXT = 'ASC' THEN name
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'name' AND sqlc.arg('order')::TEXT = 'DESC' THEN name
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'total_budget' AND sqlc.arg('order')::TEXT = 'ASC' THEN total_budget
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'total_budget' AND sqlc.arg('order')::TEXT = 'DESC' THEN total_budget
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at
END
DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at
END
ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at
END
DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: UpdateCampaign :exec
UPDATE campaigns
SET name              = $1,
    description       = $2,
    start_date        = $3,
    end_date          = $4,
    total_budget      = $5,
    thumbnail         = $6,
    short_description = $7,
    promotional_message = $8,
    social_media_links = $9
WHERE id = $10;

-- name: DeleteCampaign :exec
UPDATE campaigns
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: UserLikedCampaign :one
SELECT count(id)
from favorite_campaigns
WHERE campaign_id = $1
  AND user_id = $2;


-- name: GetCampaignSubscribers :many
WITH FILTERED AS (SELECT (cvs.user_id), u.full_name, u.image, cvs.created_at, cvs.comment FROM campaign_variant_subscriptions cvs
INNER JOIN users u ON u.id = cvs.user_id WHERE cvs.campaign_id = $1),
COUNTED AS (
SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: GetCampaignSubscriberComments :many
WITH FILTERED AS (SELECT cvs.user_id, u.full_name, u.image, cvs.created_at, cvs.id, cvs.comment FROM campaign_variant_subscriptions cvs INNER JOIN users u ON cvs.user_id = u.id 
WHERE cvs.campaign_id = $1 AND cvs.comment IS NOT NULL),
 COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY created_at DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');



-- name: GetMySupportedCampaigns :many
   WITH FILTERED AS (SELECT DISTINCT(cvs.campaign_id), c.name, c.short_description, c.created_at, cvs.created_at as supported_created_at, c.total_budget, c.start_date, c.end_date, c.vtuber_id, c.promotional_message,c.social_media_links, c.slug, 
(SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE c.id = campaign_variant_subscriptions.campaign_id) as total_raised,
 c.thumbnail FROM campaign_variant_subscriptions cvs INNER JOIN campaigns c ON c.id = cvs.campaign_id WHERE cvs.user_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY supported_created_at DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetRandomCampaign :one
SELECT c.*,
       vp.display_name   as vtuber_name,
       vp.image          as vtuber_image,
       vp.user_id        as vtuber_user_id,
       vp.furigana       as vtuber_furigana,
       vp.banner_image   as vtuber_banner_image,
       vp.social_media_links as vtuber_social_media_links,
       vp.description    as vtuber_description,
       vp.created_at as vtuber_created_at,
         (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE campaign_id = c.id) as total_raised
FROM campaigns c
         INNER JOIN vtuber_profiles vp ON c.vtuber_id = vp.id
WHERE c.deleted_at IS NULL ORDER BY RANDOM() LIMIT 1;

-- name: GetCampaignBySlug :one
SELECT * FROM campaigns WHERE slug = $1 ;