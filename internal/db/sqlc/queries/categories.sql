-- name: GetAllCategories :many
SELECT * FROM categories WHERE deleted_at IS NULL;

-- name: CreateCategory :one
INSERT INTO categories (name, description, image,slug) VALUES ($1, $2, $3,$4) RETURNING *;

-- name: GetCategoryById :one
SELECT * FROM categories WHERE (id::TEXT = sqlc.arg('id')::TEXT OR slug::TEXT = sqlc.arg('id')::TEXT) AND deleted_at IS NULL;

-- name: UpdateCategory :one
UPDATE categories SET name = $1, description = $2, image = $3 WHERE id = $4 RETURNING *;

-- name: DeleteCategory :one
UPDATE categories SET deleted_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *;

-- name: GetCategoryBySlug :one
SELECT * FROM categories WHERE slug = $1;