-- name: AddEvent :one
WITH inserted as (
    INSERT
        INTO events (category_id, vtuber_profile_id, user_id, title, description, image, rules, status, start_date,
                     end_date, short_description, participation_flow, benefits, requirements, overview, social_media_links,slug)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17) RETURNING *)
SELECT inserted.id,
       inserted.title,
       inserted.description,
       inserted.image,
       inserted.rules,
       inserted.start_date,
       inserted.end_date,
       inserted.created_at,
       inserted.category_id,
       inserted.status,
       inserted.short_description,
       inserted.participation_flow,
       inserted.benefits,
       inserted.requirements,
       inserted.overview,
       inserted.social_media_links,
       inserted.slug,
       u.id            as userId,
       u.full_Name     as userName,
       u.image         as user_image,
       vp.id           as vtuberId,
       vp.display_name as vtuberName,
       vp.image        as vtuberImage
FROM inserted
         LEFT JOIN users u ON inserted.user_id = u.id 
         LEFT JOIN vtuber_profiles vp ON inserted.vtuber_profile_id = vp.id;

-- name: GetAllEvents :many
WITH FILTERED AS (
  SELECT 
    events.id,
    events.title,
    events.description,
    events.image,
    events.rules,
    events.start_date,
    events.end_date,
    events.created_at,
    events.updated_at,
    events.status,
    events.short_description,
    events.participation_flow,
    events.benefits,
    events.requirements,
    events.overview,
    events.social_media_links,
    events.slug,
    events.category_id,
    u.id AS userId,
    u.full_name AS userName,
    u.image AS user_image,
    vp.id AS vtuberId,
    vp.display_name AS vtuberName,
    vp.image AS vtuberImage
  FROM events
  LEFT JOIN users u 
    ON events.user_id = u.id
  LEFT JOIN vtuber_profiles vp 
    ON events.vtuber_profile_id = vp.id
  WHERE 
    events.category_id = COALESCE(sqlc.narg('category_id'), events.category_id)
    AND (sqlc.narg('vtuber_id')::BIGINT IS NULL OR events.vtuber_profile_id = sqlc.narg('vtuber_id')::BIGINT)
    AND events.deleted_at IS NULL
    AND (sqlc.arg('is_admin')::boolean OR events.status = 'approved')
),
COUNTED AS (
  SELECT COUNT(*) AS total
  FROM FILTERED
)
SELECT f.*, c.total
FROM FILTERED f, COUNTED c
ORDER BY 
  CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
  CASE WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'ASC' THEN title END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'DESC' THEN title END DESC,
  CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
  CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
LIMIT sqlc.arg('limit') 
OFFSET sqlc.arg('offset');


-- name: DeleteEventById :exec
UPDATE events
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: UpdateEventById :exec
UPDATE events
SET title             = $1,
    description       = $2,
    image             = $3,
    rules             = $4,
    start_date        = $5,
    end_date          = $6,
    category_id       = $7,
    short_description = $8,
    participation_flow = $9,
    benefits          = $10,
    requirements      = $11,
    overview          = $12,
    social_media_links = $13
WHERE id = $14;

-- name: ApproveOrRejectEventById :exec
UPDATE events
SET status = $1
WHERE id = $2;


-- name: GetOneEvent :one
SELECT *
FROM events
WHERE (id::TEXT = sqlc.arg('id')::TEXT OR slug::TEXT = sqlc.arg('id')::TEXT);

-- name: GetEventById :one
SELECT events.id,
       events.title,
       events.description,
       events.image,
       events.rules,
       events.start_date,
       events.end_date,
       events.created_at,
       events.status,
       events.short_description,
       events.participation_flow,
       events.benefits,
       events.requirements,
       events.overview,
       events.social_media_links,
       events.slug,
       events.category_id,
       u.id            as user_id,
       u.full_Name     as userName,
       u.image         as user_image,
       vp.id           as vtuberId,
       vp.display_name as vtuberName,
       vp.image        as vtuberImage
FROM events
         LEFT JOIN users u ON events.user_id = u.id 
         LEFT JOIN vtuber_profiles vp ON events.vtuber_profile_id = vp.id
WHERE (events.id::TEXT = sqlc.arg('id')::TEXT OR events.slug::TEXt = sqlc.arg('id')::TEXT) 
AND events.deleted_at IS NULL;



-- name: GetMyEvents :many
WITH FILTERED AS (SELECT events.id,
                         events.title,
                         events.description,
                         events.image,
                         events.rules,
                         events.start_date,
                         events.end_date,
                         events.created_at,
                         events.updated_at,
                         events.status,
                         events.short_description,
                         events.participation_flow,
                         events.benefits,
                         events.requirements,
                         events.overview,
                         events.social_media_links,
                            events.slug,
                         events.category_id,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                         vp.id           as vtuberId,
                         vp.display_name as vtuberName,
                         vp.image        as vtuberImage
                  FROM events
                           LEFT JOIN users u ON events.user_id = u.id
                           LEFT JOIN vtuber_profiles vp
                                     ON events.vtuber_profile_id = vp.id
                  WHERE events.deleted_at IS NULL AND events.vtuber_profile_id = sqlc.arg('vtuber_id')::BIGINT),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'ASC' THEN title
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'DESC' THEN title
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at
             END
        DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetUserEvents :many
WITH FILTERED AS (SELECT events.id,
                         events.title,
                         events.description,
                         events.image,
                         events.rules,
                         events.start_date,
                         events.end_date,
                         events.created_at,
                         events.updated_at,
                         events.status,
                         events.short_description,
                         events.participation_flow,
                         events.benefits,
                         events.requirements,
                         events.overview,
                         events.social_media_links,
                         events.slug,
                         events.category_id,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image
                  FROM events
                           INNER JOIN users u ON events.user_id = u.id
                  WHERE events.deleted_at IS NULL AND events.user_id = sqlc.arg('user_id')::BIGINT),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at
             END
        DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetEventBySlug :one
SELECT * from events
WHERE slug = $1;
