import { useLanguage } from "@vtuber/language/hooks";
import { But<PERSON> } from "@vtuber/ui/components/button";

interface Props {
  onNext: () => void;
}

export const AuthenticationSuccessMessage = ({ onNext }: Props) => {
  const { getText } = useLanguage();
  return (
    <div className="max-w-[350px] flex flex-col gap-y-[20px] text-font">
      <h3 className="text-[26px] font-bold">{getText("auth_completed")}</h3>
      <p>{getText("auth_completed_message")}</p>
      <Button
        onClick={onNext}
        variant={"tertiary"}
        className="rounded-full w-full h-[48px] font-bold text-white">
        {getText("register_account_info")}
      </Button>
    </div>
  );
};
