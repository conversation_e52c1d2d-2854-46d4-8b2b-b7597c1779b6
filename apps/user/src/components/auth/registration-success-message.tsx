import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { motion } from "motion/react";

export const RegistrationSuccessMessage = () => {
  const router = useRouter();
  const { getText } = useLanguage();
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="max-w-[350px] flex flex-col gap-y-[20px] text-font">
      <h3 className="text-[26px] font-bold">
        {getText("registration_finished")}
      </h3>
      <p>{getText("registration_finished_message")}</p>
      <section className="flex flex-col gap-y-8">
        <Button
          onClick={() => {
            router.navigate({ to: "/login" });
          }}
          className="rounded-full w-full h-[48px] font-bold">
          {getText("login")}
        </Button>
        <Button
          variant={"tertiary"}
          onClick={() => {
            router.navigate({ to: "/" });
          }}
          className="w-full h-[48px] font-bold rounded-full text-white">
          {getText("return_to_top_page")}
        </Button>
      </section>
    </motion.div>
  );
};
