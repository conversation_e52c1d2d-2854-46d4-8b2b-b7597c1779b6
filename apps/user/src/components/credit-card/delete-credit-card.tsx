import { useMutation } from "@connectrpc/connect-query";
import { useQueryClient } from "@tanstack/react-query";
import { useLanguage } from "@vtuber/language/hooks";
import { BillInfoService } from "@vtuber/services/billing";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  AlertDialogTriggerProps,
} from "@vtuber/ui/components/alert-dialog";
import { Spinner } from "@vtuber/ui/components/spinner";
import { useState } from "react";
import { toast } from "sonner";

export const DeleteCreditCard = (
  props: AlertDialogTriggerProps & { cardId: bigint },
) => {
  const queryClient = useQueryClient();

  const [open, setOpen] = useState(false);
  const { getText } = useLanguage();
  const { mutate: deleteCardMutation, isPending: deletingCard } = useMutation(
    BillInfoService.method.deleteBillingInfo,
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries({
          queryKey: ["credit_card"],
        });
        toast.success(data.message);
        setOpen(false);
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const deleteCard = () => {
    deleteCardMutation({
      id: props.cardId,
    });
  };

  return (
    <AlertDialog
      open={open}
      onOpenChange={setOpen}>
      <AlertDialogTrigger {...props} />
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {getText("delete_credit_card_alert_title")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {getText("delete_credit_card_alert_description")}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deletingCard}>
            {getText("cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={deleteCard}
            disabled={deletingCard}>
            {deletingCard && <Spinner className="size-4 mr-1" />}
            {getText("confirm")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
