import { Link } from "@tanstack/react-router";
import { Campaign } from "@vtuber/services/campaigns";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@vtuber/ui/components/carousel";
import { Image } from "@vtuber/ui/components/image";

export const HomeCampaignList = ({ campaigns }: { campaigns: Campaign[] }) => {
  return (
    <Carousel
      opts={{
        align: "start",
        dragFree: true,
        startIndex: 1,
      }}>
      <CarouselContent className="gap-x-[25px]">
        {campaigns.map((campaign) => (
          <CarouselItem
            className="lg:basis-1/5 sm:basis-1/3 basis-[45%] py-1 flex justify-center items-center"
            key={campaign.id}>
            <Link
              to={`/campaign/$id`}
              params={{
                id: campaign.slug,
              }}
              className="w-full">
              <Image
                style={{
                  filter: "drop-shadow(0px 0px 6px #F7F5F5)",
                }}
                src={campaign.thumbnail || ""}
                alt={campaign.name}
                className="aspect-video rounded-10 w-full object-cover object-center"
              />
            </Link>
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};
