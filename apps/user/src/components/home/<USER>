import { useQuery } from "@connectrpc/connect-query";
import { AnnouncementsService } from "@vtuber/services/cms";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
} from "@vtuber/ui/components/dialog";
import { Image } from "@vtuber/ui/components/image";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { Spinner } from "@vtuber/ui/components/spinner";
import { CAN_USE_DOM } from "@vtuber/ui/lib/utils";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { ANNOUNCEMENT_POPUP_KEY } from "~/data/constants";

export const AnnouncementPopup = () => {
  const { data, isPending } = useQuery(
    AnnouncementsService.method.getAnnouncement,
    {},
  );
  const key = CAN_USE_DOM
    ? sessionStorage.getItem(ANNOUNCEMENT_POPUP_KEY)
    : null;
  const showAnnouncement = !key && key !== "hidden";
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (data?.data && data.data.active && showAnnouncement) {
      setOpen(true);
    }
  }, [data]);

  const hide = () => {
    setOpen(false);
    if (CAN_USE_DOM) {
      sessionStorage.setItem(ANNOUNCEMENT_POPUP_KEY, "hidden");
    }
  };

  return (
    <Dialog
      modal={false}
      open={open}
      onOpenChange={hide}>
      <DialogContent
        portalContent={
          <>
            <div
              className="fixed top-0 left-0 w-full h-full bg-black/80 z-50"
              onClick={hide}
            />
            <DialogClose asChild>
              <Button
                variant={"ghost"}
                className="fixed top-2 z-[9999999999999] right-10 p-0 size-12 cursor-pointer">
                <X className="fill-white text-white !size-8" />
              </Button>
            </DialogClose>
          </>
        }
        withCloseButton={false}
        className="w-[92vw] max-w-[1080px] min-h-[50vh] focus-visible:border-none outline-none border-none flex items-center justify-center !rounded-none">
        {isPending ? (
          <div className="bg-background md:w-[60%] sm:w-[80%] w-[90%] md:h-[60dvh] sm:h-[40dvh] h-[25dvh] flex items-center justify-center">
            <Spinner className="size-20" />
          </div>
        ) : (
          <ScrollArea className="flex flex-col size-full overflow-y-auto max-h-[80dvh]">
            <div className="flex w-full items-center mx-auto justify-center">
              <div className="space-y-6 w-full">
                <AspectRatio ratio={699 / 393}>
                  <Image
                    className="w-full"
                    src={data?.data?.image || ""}
                    alt={data?.data?.content || ""}
                  />
                </AspectRatio>
                <p className="text-font">{data?.data?.content}</p>
              </div>
            </div>
          </ScrollArea>
        )}
      </DialogContent>
    </Dialog>
  );
};
