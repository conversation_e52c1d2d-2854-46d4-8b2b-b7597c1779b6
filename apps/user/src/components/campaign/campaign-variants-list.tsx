import { CampaignVariantById } from "@vtuber/services/campaigns";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { CampaignVariantCard } from "./campaign-variant-card";

export const CampaignVariantsList = ({
  variants,
}: {
  variants?: CampaignVariantById[];
}) => {
  if (!variants || variants.length === 0) return null;
  return (
    <div
      className="flex flex-col gap-y-10"
      id="support">
      <DisplayTag text="select_by_return" />
      <div className="space-y-10">
        {variants.map((v) => (
          <CampaignVariantCard
            key={v.id}
            variant={v}
          />
        ))}
      </div>
    </div>
  );
};
