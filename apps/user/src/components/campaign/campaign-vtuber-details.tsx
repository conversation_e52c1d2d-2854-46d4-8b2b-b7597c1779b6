import { GetCampaignById } from "@vtuber/services/campaigns";
import { Avatar } from "@vtuber/ui/components/avatar";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { ExpandableText } from "@vtuber/ui/components/expandable-text";
import { cn } from "@vtuber/ui/lib/utils";

export const CampaignVtuberDetails = ({
  campaign,
  className,
}: {
  campaign?: GetCampaignById;
  className?: string;
}) => {
  return (
    <Card
      style={{
        filter: "drop-shadow(0px 0px 15px rgba(0, 0, 0, 0.25))",
      }}
      className={cn(
        "bg-background rounded-10 px-4 py-6 border border-[#A9A9A9]",
        className,
      )}>
      <CardHeader className="items-center gap-y-3 p-0 text-center">
        <Avatar
          src={campaign?.vtuber?.image}
          fallback={campaign?.vtuber?.displayName}
          alt={campaign?.vtuber?.displayName}
          className="size-[146px]"
        />
        <CardTitle className="text-2xl text-[#777777]">
          {campaign?.vtuber?.displayName}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 pt-6">
        <ExpandableText
          className="text-sm font-medium text-font"
          buttonClassName="mt-3"
          length={200}
          text={campaign?.vtuber?.description || ""}
        />
      </CardContent>
    </Card>
  );
};
