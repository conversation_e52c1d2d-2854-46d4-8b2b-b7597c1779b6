import { useLanguage } from "@vtuber/language/hooks";
import { GetCampaignById } from "@vtuber/services/campaigns";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Progress } from "@vtuber/ui/components/progress";
import { RemainingDays } from "@vtuber/ui/components/remaining-days";
import { cn, getProgress } from "@vtuber/ui/lib/utils";

export const CampaignDetailsCard = ({
  campaign,
  className,
}: {
  campaign: GetCampaignById;
  className?: string;
}) => {
  const { getText } = useLanguage();

  const progress = getProgress(campaign.totalBudget, campaign.totalRaised);

  return (
    <Card
      style={{
        filter: "drop-shadow(0px 0px 15px #FFE2BB)",
      }}
      className={cn(
        "bg-sub shadow-none border-none py-[39px] px-8 rounded-10 text-font grid gap-y-6",
        className,
      )}>
      <CardHeader className="p-0">
        <small className="text-sm font-medium">
          {getText("current_support_amount")}
        </small>
        <CardTitle className="font-medium space-y-3 pt-2">
          <p className="text-[42px]">
            {campaign.totalRaised.toLocaleString()}
            <span className="text-[31px]"> {getText("yen")}</span>
          </p>
          <p>
            {getText("target_amount")}: {campaign?.totalBudget.toLocaleString()}
          </p>
        </CardTitle>
        <div className="space-y-1 pt-4">
          <div className="flex items-center justify-between text-xs">
            <p>{getText("progress")}</p>
            <p className="font-medium text-[#D9D9D9]">{progress}%</p>
          </div>
          <Progress value={progress} />
        </div>
      </CardHeader>
      <CardContent className="p-0 grid gap-y-6">
        <div className="flex justify-end">
          <RemainingDays
            className="text-lg"
            daysClassname="text-[32px] font-bold"
            endDate={campaign?.endDate!}
            startDate={campaign?.startDate!}
          />
        </div>
        <Button
          size={"lg"}
          variant={"tertiary"}
          asChild
          className="text-white font-bold w-full">
          <a href="#support">{getText("support_this_project")}</a>
        </Button>
      </CardContent>
    </Card>
  );
};
