import { GetCampaignByIdResponse } from "@vtuber/services/campaigns";
import { MarkDown } from "@vtuber/ui/components/markdown";

export const CrowdfundingOverview = ({
  campaign,
}: {
  campaign?: GetCampaignByIdResponse["data"];
}) => {
  return (
    <div className="space-y-10">
      <MarkDown
        markdown={campaign?.shortDescription}
        className="text-lg font-medium"
      />
    </div>
  );
};
