import { useQuery } from "@tanstack/react-query";
import { GetAllCampaignsResponse } from "@vtuber/services/campaigns";
import { RouteFallback } from "@vtuber/ui/components/route-fallback";
import { useState } from "react";
import { campaignsQueryOptions } from "~/utils/api";
import { LoadMoreButton } from "../load-more-button";
import { CampaignCardSkeleton } from "../skeletons/campaign-card-skeleton";
import { CampaignCard } from "./campaign-card";

export const CampaignList = ({
  initialData,
}: {
  initialData: GetAllCampaignsResponse;
}) => {
  const [campaignSize, setCampaignSize] = useState(20);
  const { data, isPending, isFetching, isRefetching } = useQuery(
    campaignsQueryOptions({
      size: campaignSize,
      initialData,
    }),
  );
  const campaigns = data?.data;
  const noCampaigns = !campaigns || campaigns.length === 0;

  if (isPending)
    return (
      <div className="grid sm:gap-y-16 gap-y-12">
        <section className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 sm:gap-x-6 sm:gap-y-10 gap-y-8">
          {Array.from({ length: 9 }).map((_, i) => (
            <CampaignCardSkeleton key={i} />
          ))}
        </section>
        <LoadMoreButton
          disabled
          itemsLength={0}
          totalItems={0}
        />
      </div>
    );

  if (noCampaigns)
    return (
      <RouteFallback
        className="pt-20"
        data={{ data: "No campaigns found" }}
      />
    );

  return (
    <div className="grid sm:gap-y-16 gap-y-12">
      <section className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 sm:gap-x-6 sm:gap-y-10 gap-y-8">
        {campaigns?.map((c) => (
          <CampaignCard
            key={c.id.toString()}
            campaign={c}
          />
        ))}

        {isFetching &&
          [0, 1, 2].map((_, i) => <CampaignCardSkeleton key={i} />)}
      </section>

      <LoadMoreButton
        itemsLength={data?.data?.length || 0}
        totalItems={data?.paginationDetails?.totalItems || 0}
        onClick={() => {
          setCampaignSize((prev) => prev + 3);
        }}
        className={
          "bg-transparent text-tertiary hover:bg-transparent p-0 font-bold relative after:absolute after:content-[''] after:w-full after:bg-tertiary after:h-[2px] after:left-0 after:-bottom-1"
        }
      />
    </div>
  );
};
