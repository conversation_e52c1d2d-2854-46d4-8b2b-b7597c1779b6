import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { CampaignVariantById } from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { But<PERSON> } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Users } from "lucide-react";

export const CampaignVariantCard = ({
  variant,
}: {
  variant: CampaignVariantById;
}) => {
  const { getText, language } = useLanguage();
  const router = useRouter();

  return (
    <Card className="rounded-10 border-none shadow-none bg-gradient-3 py-[39px] px-8 space-y-6">
      <CardHeader className="gap-y-4 p-0">
        <AspectRatio ratio={288 / 208}>
          <Image
            className="size-full rounded-[22px] object-cover"
            src={variant.image}
            alt={variant.title}
          />
        </AspectRatio>

        <div className="text-font text-center space-y-1.5">
          <h6 className="text-xl font-bold">{variant.title}</h6>
          <CardTitle className="text-[29px] font-bold">
            {variant.price.toLocaleString()}{" "}
            <span className="text-lg font-medium"> {getText("yen")}</span>{" "}
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 p-0">
        <Button
          variant={variant.hasSubscribed ? "sub-outline" : "tertiary"}
          size={"lg"}
          disabled={variant.hasSubscribed}
          className="w-full text-white font-bold"
          onClick={() => {
            if (variant.hasSubscribed) return;
            router.navigate({
              to: "/payment",
              search: {
                price: variant.price,
                variantId: Number(variant.id),
                campaignId: Number(variant.campaignId),
              },
              mask: {
                to: "/payment",
                search: {
                  price: undefined,
                  variantId: undefined,
                },
              },
            });
          }}>
          {variant.hasSubscribed ? "subscribed" : getText("support")}
        </Button>
        <MarkDown
          markdown={variant.description}
          className="text-font"
        />
      </CardContent>
      <CardFooter className="p-0 flex-row justify-end">
        <p className="text-white font-medium">
          {getText("no_of_supporters")}{" "}
          <span className="font-bold text-[32px] text-[#F6C172]">
            {variant.subCount}
          </span>{" "}
          {language === "ja" ? (
            "人"
          ) : (
            <Users
              size={18}
              className="inline mb-2 text-font"
            />
          )}
        </p>
      </CardFooter>
    </Card>
  );
};
