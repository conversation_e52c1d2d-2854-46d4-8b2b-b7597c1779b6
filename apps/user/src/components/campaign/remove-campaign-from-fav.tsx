import { useMutation } from "@connectrpc/connect-query";
import {
  FavoriteCampaignService,
  FavoriteCampaignWithDetails,
} from "@vtuber/services/socials";
import { Button } from "@vtuber/ui/components/button";
import { toast } from "sonner";

export const RemoveFavoriteCampaign = ({
  data,
  setFavoriteVtubers,
}: {
  data: FavoriteCampaignWithDetails;
  setFavoriteVtubers: React.Dispatch<
    React.SetStateAction<FavoriteCampaignWithDetails[] | undefined>
  >;
}) => {
  const { mutate, isPending } = useMutation(
    FavoriteCampaignService.method.deleteFavoriteCampaign,
    {
      onSuccess: (_, { campaignId }) => {
        setFavoriteVtubers((prev) =>
          prev?.filter((fv) => fv.campaignId !== campaignId),
        );
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  return (
    <Button
      loading={isPending}
      onClick={() => {
        mutate({ campaignId: data.campaignId });
      }}
      className="w-full"
      variant={"destructive-outline"}>
      Remove from favorites
    </Button>
  );
};
