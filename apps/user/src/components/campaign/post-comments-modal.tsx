import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useQuery } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { PostCommentService, PostService } from "@vtuber/services/content";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button, buttonVariants } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Media } from "@vtuber/ui/components/media";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { Separator } from "@vtuber/ui/components/separator";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { getTimeAgo } from "@vtuber/ui/lib/get-time-ago";
import { cn } from "@vtuber/ui/lib/utils";
import { MessageCircle } from "lucide-react";
import { useRef, useState } from "react";
import { LikePost } from "../post/like-post";
import { PostCommentForm } from "../post/post-comment-form";
import { PostComments } from "../post/post-comments";

type Props = React.ComponentProps<typeof DialogTrigger> & {
  postId: bigint;
  opened?: boolean;
  slug: string;
};

export const PostCommentsModal = ({ postId, slug, ...props }: Props) => {
  const { getText } = useLanguage();
  const { isMobile } = useIsMobile();
  const [opened, setOpened] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const router = useRouter();
  const {
    isPending: loadingPost,
    data: postData,
    error,
    refetch: refetchPost,
    isRefetching: isRefetchingPost,
  } = useQuery(
    PostService.method.getPostById,
    {
      id: slug,
    },
    {
      enabled: !!slug && opened,
    },
  );
  const { data, isPending, refetch } = useQuery(
    PostCommentService.method.getAllPostComments,
    {
      postId: slug,
      pagination: {
        size: 100,
        order: "desc",
        sort: "created_at",
      },
    },
  );

  const comments = data?.data;

  const commentsAvailable = !isPending && comments && comments?.length > 0;
  const post = postData?.data;
  return (
    <Dialog
      open={opened}
      onOpenChange={setOpened}>
      <DialogTrigger {...props} />
      <DialogContent className="lg:max-w-[40%] sm:max-w-[80%] w-[90%] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="sm:text-2xl text-lg text-center">
            {isPending ? getText("loading") + "..." : post?.title}
          </DialogTitle>
        </DialogHeader>
        <Separator />

        {loadingPost || isRefetchingPost ? (
          <div className="flex items-center justify-center h-[75dvh]">
            {getText("loading")}...
          </div>
        ) : !post || error ? (
          <div className="flex items-center justify-center h-[75dvh]">
            <div className="space-y-3 text-center">
              <p className="text-2xl">
                {error ? error.rawMessage : getText("post_not_found")}
              </p>
              <Button
                onClick={() => {
                  refetchPost();
                }}
                size={"lg"}
                variant={"outline-dark"}>
                {getText("try_again")}
              </Button>
            </div>
          </div>
        ) : (
          <ScrollArea className="flex flex-col overflow-y-auto max-h-[75dvh] p-6 pb-0">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <Avatar
                  className="h-14 w-14 text-xl"
                  src={post.vtuber?.image}
                  alt={post.vtuber?.name}
                  fallback={post.vtuber?.name}
                />
                <div>
                  <h3 className="text-lg font-bold">{post.vtuber?.name}</h3>
                  <DialogDescription>
                    {getTimeAgo(timestampDate(post.createdAt!))}
                  </DialogDescription>
                </div>
              </div>

              <DialogDescription className="text-foreground">
                {post.shortDescription}
              </DialogDescription>
              {post.media && (
                <div className="lg:h-[50dvh] sm:h-[40dvh] h-48 py-3 w-full">
                  <Media
                    style={{
                      viewTransitionName: `post-${postId}`,
                    }}
                    onClick={() => {
                      router.navigate({
                        to: "/vtuber/post/$id",
                        params: {
                          id: post.slug,
                        },
                      });
                    }}
                    className="rounded-xl w-full aspect-auto"
                    src={post.media}
                    type={post.mediaType}
                    alt={post.name}
                    controls
                    showControls={!isMobile}
                  />
                </div>
              )}
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <LikePost
                className={cn(
                  buttonVariants({ variant: "ghost" }),
                  "flex-1 flex items-center justify-center gap-x-1 [&_svg]:!size-4 hover:bg-transparent hover:text-white/50",
                )}
                postId={post.id}
                postLikes={post.postLikes}
                hasLiked={post.hasLiked}
              />
              <Button
                onClick={() => {
                  if (inputRef.current) {
                    inputRef.current.focus();
                  }
                }}
                variant={"ghost"}
                className="flex-1 hover:bg-transparent hover:text-white/50">
                <MessageCircle className="mr-1" /> {getText("comment")} (
                {post.postComments})
              </Button>
            </div>
            <Separator className="mb-5" />
            {isPending ? (
              <div className="h-40 flex justify-center items-center">
                {getText("loading")}...
              </div>
            ) : commentsAvailable ? (
              <div className="space-y-4 mt-10">
                {comments?.map((comment) => {
                  return (
                    <PostComments
                      comment={comment}
                      refetch={refetch}
                      key={comment.id}
                      creatorId={post.vtuber?.id || 0n}
                    />
                  );
                })}
              </div>
            ) : (
              <div className="h-40 flex justify-center items-center text-center text-gray-400 text-2xl">
                {getText("no_comments_yet")}
              </div>
            )}
          </ScrollArea>
        )}
        <DialogFooter className="block w-full pb-3 px-6">
          <PostCommentForm
            postId={postId}
            refetch={refetch}
            ref={inputRef}
          />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
