import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { GetCampaignById } from "@vtuber/services/campaigns";
import { FavoriteCampaignService } from "@vtuber/services/socials";
import { Button } from "@vtuber/ui/components/button";
import { Spinner } from "@vtuber/ui/components/spinner";
import { Heart } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export const ToggleFavCampaign = ({
  campaign,
}: {
  campaign: GetCampaignById;
}) => {
  const { getText } = useLanguage();
  const { session } = useAuth();
  const router = useRouter();
  const [isLiked, setIsLiked] = useState(campaign.hasLiked);

  const { mutate: addFavoriteCampaign, isPending: addingFavoriteCampaign } =
    useMutation(FavoriteCampaignService.method.addFavoriteCampaign, {
      onSuccess: () => {
        toast.success("campaign added to favorites");
      },
      onError: (err) => {
        toast.error(err.message);
        setIsLiked((prev) => !prev);
      },
    });
  const {
    mutate: deleteFavoriteCampaign,
    isPending: deletingFavoriteCampaign,
  } = useMutation(FavoriteCampaignService.method.deleteFavoriteCampaign, {
    onSuccess: (data) => {
      toast.success(data.message);
    },
    onError: (err) => {
      toast.error(err.message);
      setIsLiked((prev) => !prev);
    },
  });

  const isLoading = addingFavoriteCampaign || deletingFavoriteCampaign;

  return (
    <Button
      onClick={() => {
        if (!session) {
          router.navigate({
            to: "/login",
            search: {
              redirect: location.pathname,
            },
          });
          return;
        }

        setIsLiked((prev) => !prev);
        if (isLiked) {
          deleteFavoriteCampaign({
            campaignId: campaign.id,
          });
        } else {
          addFavoriteCampaign({ campaignId: campaign.id });
        }
      }}
      variant={"blue-outline"}
      className="rounded-full h-[47px] text-sm font-bold">
      {isLoading ? (
        <Spinner className="!size-6 mr-3" />
      ) : (
        <Heart
          className={`!size-[21px] mr-3 ${isLiked ? "fill-red-500 text-red-500" : ""}`}
        />
      )}
      {isLiked ? getText("remove_from_favorites") : getText("add_to_favorites")}
    </Button>
  );
};
