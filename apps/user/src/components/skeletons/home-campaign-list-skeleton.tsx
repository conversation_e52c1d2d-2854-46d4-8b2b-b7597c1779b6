import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@vtuber/ui/components/carousel";
import { Skeleton } from "@vtuber/ui/components/skeleton";

export const HomeCampaignListSkeleton = () => {
  return (
    <Carousel
      className="relative z-50"
      opts={{
        align: "start",
        dragFree: true,
        startIndex: 1,
      }}>
      <CarouselContent className="gap-x-[25px]">
        {[0, 1, 2, 3, 4].map((_, i) => (
          <CarouselItem
            className="lg:basis-1/5 sm:basis-1/3 basis-[45%] py-1 flex justify-center items-center"
            key={i}>
            <Skeleton
              style={{
                filter: "drop-shadow(0px 0px 6px #F7F5F5)",
              }}
              className="aspect-video rounded-10 w-full"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};
