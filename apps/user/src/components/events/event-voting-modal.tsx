import { useQuery } from "@tanstack/react-query";
import { Link, useLocation } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { eventVoteServiceClient, usersClient } from "@vtuber/services/client";
import {
  Button,
  ButtonProps,
  buttonVariants,
} from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Spinner } from "@vtuber/ui/components/spinner";
import { cn } from "@vtuber/ui/lib/utils";
import { EventVotingFinalStep } from "./event-voting/event-voting-final-step";
import { useEventVotingModal } from "./event-voting/event-voting-modal-provider";
import { EventVotingStep1 } from "./event-voting/event-voting-modal-step1";
import { EventVotingStep2 } from "./event-voting/event-voting-modal-step2";
import { EventVotingStep3 } from "./event-voting/event-voting-modal-step3";

interface Props extends ButtonProps {
  eventId: bigint;
  eventParticipationId: bigint;
}

export const EventVotingModal = ({
  className,
  eventId,
  eventParticipationId,
  ...props
}: Props) => {
  const { setStep, step, opened, setOpened } = useEventVotingModal();
  const { session } = useAuth();
  const { pathname } = useLocation();
  const { getText } = useLanguage();
  const { data, isPending } = useQuery({
    queryKey: ["daily-voting"],
    queryFn: async () => {
      const [res] = await eventVoteServiceClient.getDailyPointAvailable({
        eventId,
      });
      return res;
    },
    enabled: opened,
  });

  const { data: points } = useQuery({
    queryKey: ["points"],
    queryFn: async () => {
      const [data] = await usersClient.getUserPoint({});
      return data;
    },
    enabled: opened,
  });

  if (!session)
    return (
      <Link
        to="/login"
        search={{
          redirect: pathname,
        }}
        className={cn(
          buttonVariants({
            variant: "outline",
          }),
          "!rounded-full w-full !text-xs !font-medium",
        )}>
        {getText("vote")}
      </Link>
    );

  return (
    <Dialog
      open={opened}
      onOpenChange={(o) => {
        setOpened(o);
        setStep(0);
      }}>
      <DialogTrigger asChild>
        <Button
          variant={"outline"}
          {...props}
          className={cn("rounded-full w-full font-bold", className)}>
          {getText("vote")}
        </Button>
      </DialogTrigger>
      <DialogContent
        withCloseButton={false}
        className="bg-background rounded-10 max-w-[396px] p-0 border-none">
        {isPending ? (
          <div className="h-52 flex items-center justify-center">
            <Spinner />
          </div>
        ) : (
          <div>
            {step === 0 && <EventVotingStep1 />}
            {step === 1 && (
              <EventVotingStep2 points={Math.floor(points?.point || 0)} />
            )}
            {step === 2 && (
              <EventVotingStep3
                canVoteToday={data?.available}
                points={Math.floor(points?.point || 0)}
                eventParticipationId={eventParticipationId}
              />
            )}
            {step === 3 && <EventVotingFinalStep />}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
