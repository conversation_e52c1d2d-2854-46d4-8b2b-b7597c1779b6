import { useQuery } from "@tanstack/react-query";
import { GetAllEventsResponse } from "@vtuber/services/events";
import { RouteFallback } from "@vtuber/ui/components/route-fallback";
import { useState } from "react";
import { eventsQueryOptions } from "~/utils/api";
import { LoadMoreButton } from "../load-more-button";
import { EventCardSkeleton } from "../skeletons/event-card-skeleton";
import { EventCard } from "./event-card";

export const EventsList = ({
  events: initialEvents,
}: {
  events: GetAllEventsResponse;
}) => {
  const [eventSize, setEventSize] = useState(9);

  const { data, isFetching, isPending } = useQuery(
    eventsQueryOptions({
      size: eventSize,
      sort: "created_at",
      initialData: initialEvents,
    }),
  );

  const events = data?.data;
  const noEvents = !events || events.length === 0;

  if (isPending)
    return (
      <div className="grid sm:gap-y-16 gap-y-12">
        <section className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 sm:gap-x-6 sm:gap-y-10 gap-y-8">
          {Array.from({ length: 9 }).map((_, i) => (
            <EventCardSkeleton key={i} />
          ))}
        </section>
        <LoadMoreButton
          disabled
          itemsLength={0}
          totalItems={0}
        />
      </div>
    );

  if (noEvents) return <RouteFallback data={{ data: "No Events Found" }} />;

  return (
    <div className="grid sm:gap-y-16 gap-y-12">
      <section className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 sm:gap-x-6 sm:gap-y-10 gap-y-8">
        {events.map((e) => (
          <EventCard
            className="rounded-10"
            key={e.id.toString()}
            event={e}
          />
        ))}
        {isFetching && [0, 1, 2].map((_, i) => <EventCardSkeleton key={i} />)}
      </section>
      <LoadMoreButton
        disabled={isFetching || isPending}
        itemsLength={data?.data?.length || 0}
        totalItems={data?.paginationDetails?.totalItems || 0}
        onClick={() => {
          setEventSize((prev) => prev + 9);
        }}
      />
    </div>
  );
};
