import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation, useQuery } from "@connectrpc/connect-query";
import { useAuth } from "@vtuber/auth/hooks";
import { EventComment, EventCommentService } from "@vtuber/services/events";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button } from "@vtuber/ui/components/button";
import { Textarea } from "@vtuber/ui/components/textarea";
import { getTimeAgo } from "@vtuber/ui/lib/get-time-ago";
import { cn } from "@vtuber/ui/lib/utils";
import { useState } from "react";
import { toast } from "sonner";
import { EventCommentForm } from "./event-comment-form";

interface CampaignCommentsProps {
  comment: EventComment;
  depth?: number;
  refetch?: () => void;
  eventVtuberId?: bigint;
}

export const EventComments = ({
  comment,
  depth = 0,
  refetch,
  eventVtuberId,
}: CampaignCommentsProps) => {
  const [eventComment, setEventComment] = useState(comment.content);
  const { session } = useAuth();

  const canDelete =
    session?.vtuber?.id === eventVtuberId ||
    comment.user?.id === session?.user?.id;
  const [isReplying, setIsReplying] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showReplies, setShowReplies] = useState(false);
  const user = comment?.vtuber ? comment.vtuber : comment.user;
  const {
    data,
    isPending,
    refetch: refetchReplies,
  } = useQuery(
    EventCommentService.method.getAllRepliesOfEventComment,
    {
      id: comment.id,
      pagination: {
        size: 100,
        order: "desc",
        sort: "created_at",
      },
    },
    {
      enabled: showReplies,
    },
  );

  const updateCommentMutations = useMutation(
    EventCommentService.method.updateEventCommentById,
    {
      onSuccess: () => {
        refetch && refetch();
        setIsEditing(false);
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const deleteMutations = useMutation(
    EventCommentService.method.deleteEventCommentById,
    {
      onSuccess: () => {
        refetchReplies();
        refetch && refetch();
      },
      onError: (err) => {
        toast.error(err.message);
      },
    },
  );

  const replies = data?.data;

  return (
    <div className="relative">
      <div className={`flex max-w-full gap-2 z-10 ${depth > 0 ? "ml-1" : ""}`}>
        <Avatar
          src={user?.image}
          alt={user?.name}
          fallback={user?.name}
          className="rounded-full border-border border"
        />
        <div className="w-full">
          <div
            className="bg-border border border-border rounded-2xl px-4 py-2"
            id={`event-comment-content-${comment.id}`}>
            <div className="font-semibold text-sm">{user?.name}</div>
            {isEditing ? (
              <div className="relative">
                <Textarea
                  className="bg-background h-auto focus-visible:ring-transparent sm:placeholder:text-base text-sm my-2 overflow-hidden min-h-[40px] pt-4 pb-0 rounded-full resize-none pr-28"
                  placeholder="Edit your comment"
                  onInput={(e) => {
                    e.currentTarget.style.height = "auto";
                    e.currentTarget.style.height = `${e.currentTarget.scrollHeight}px`;
                    e.currentTarget.style.borderRadius = "24px";
                    e.currentTarget.style.paddingBottom = "0.5rem";
                  }}
                  autoFocus
                  value={eventComment}
                  onChange={(e) => setEventComment(e.target.value)}
                />
                <Button
                  loading={updateCommentMutations.isPending}
                  className="absolute z-30 bottom-2 right-2 rounded-full"
                  disabled={updateCommentMutations.isPending}
                  onClick={() => {
                    updateCommentMutations.mutate({
                      id: comment.id,
                      content: eventComment,
                    });
                  }}>
                  {updateCommentMutations.isPending ? "updating..." : "update"}
                </Button>
              </div>
            ) : (
              <p className="text-sm text-wrap w-fit break-words break-all">
                {comment.content}
              </p>
            )}
          </div>

          <div className="flex gap-4 text-xs text-gray-500 mt-1 items-center">
            <button
              className={cn("hover:text-blue-600 font-semibold", {
                "text-blue-600": isReplying,
              })}
              onClick={() => {
                setIsReplying((prev) => !prev);
              }}>
              {isReplying ? "Cancel" : "Reply"}
            </button>

            {comment.hasReply && (
              <button
                className={cn("hover:text-blue-600 font-semibold", {
                  "text-blue-600": showReplies,
                })}
                onClick={() => {
                  setShowReplies((prev) => !prev);
                }}>
                {showReplies ? "Hide " : "View "} Replies{" "}
                {showReplies ? "↑" : "↓"}
              </button>
            )}
            {canDelete && (
              <button
                className={"hover:text-destructive font-semibold"}
                onClick={() => {
                  deleteMutations.mutate({
                    id: comment.id,
                  });
                }}>
                {deleteMutations.isPending ? "Deleting..." : "Delete"}
              </button>
            )}
            {comment.user?.id === session?.user?.id && (
              <button
                className={cn(
                  "hover:text-blue-600 font-semibold",
                  isEditing && "text-blue-600",
                )}
                onClick={() => {
                  setEventComment(comment.content);
                  setIsEditing((prev) => !prev);
                }}>
                {!isEditing ? "Edit" : "Cancel"}
              </button>
            )}
            <span>{getTimeAgo(timestampDate(comment.createdAt!))}</span>
          </div>
          {showReplies && (
            <div className="space-y-3 mt-5">
              {isPending && <div>Loading...</div>}
              {!isPending &&
                replies?.map((r) => (
                  <div key={r.id}>
                    <EventComments
                      comment={r}
                      depth={depth + 1}
                      refetch={refetchReplies}
                      eventVtuberId={eventVtuberId}
                    />
                  </div>
                ))}
            </div>
          )}
          {isReplying && (
            <EventCommentForm
              setShowReplies={setShowReplies}
              refetch={async () => {
                refetchReplies();
                refetch && refetch();
              }}
              className="mt-3"
              eventId={comment.eventId}
              parentId={comment.id}
            />
          )}
        </div>
      </div>
    </div>
  );
};
