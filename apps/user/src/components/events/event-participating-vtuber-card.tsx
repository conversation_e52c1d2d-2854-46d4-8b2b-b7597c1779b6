import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { EventParticipation } from "@vtuber/services/events";
import { Image } from "@vtuber/ui/components/image";
import { ChevronRight } from "lucide-react";
import { EventVotingModal } from "./event-voting-modal";
import { EventVotingModalProvider } from "./event-voting/event-voting-modal-provider";

export const EventParticipatingVtuberCard = ({
  data,
}: {
  data?: EventParticipation;
}) => {
  const { getText } = useLanguage();
  return (
    <EventVotingModalProvider data={data}>
      <div className="flex items-start gap-3">
        <Image
          src={data?.vtuber?.image}
          className="rounded-10 bg-[#2C2A37] border border-[#707070] h-[67px] w-[118px]"
        />
        <div className="flex-1 flex flex-col gap-y-2">
          <h6 className="font-bold text-font text-lg">{data?.vtuber?.name}</h6>
          <EventVotingModal
            eventId={data?.eventId || 0n}
            eventParticipationId={data?.id || 0n}
          />
          <Link
            to="/vtuber/$id"
            params={{
              id: data?.vtuber?.id.toString() || "",
            }}
            className="flex items-center justify-between text-primary w-full text-sm font-bold">
            {getText("view_profile")} <ChevronRight />
          </Link>
        </div>
      </div>
    </EventVotingModalProvider>
  );
};
