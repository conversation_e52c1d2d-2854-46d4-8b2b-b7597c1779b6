import { useLanguage } from "@vtuber/language/hooks";
import { Event } from "@vtuber/services/events";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { MarkDown } from "@vtuber/ui/components/markdown";

export const EventOverview = ({ event }: { event: Event }) => {
  const { getText } = useLanguage();
  return (
    <section className="grid gap-y-8">
      <DisplayTag text="event_overview" />
      <MarkDown
        className="text-font"
        markdown={event.overview}
      />
    </section>
  );
};
