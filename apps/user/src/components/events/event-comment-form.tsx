import { useMutation } from "@connectrpc/connect-query";
import { RefetchOptions } from "@tanstack/react-query";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { EventCommentService } from "@vtuber/services/events";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button } from "@vtuber/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@vtuber/ui/components/dropdown-menu";
import { Form } from "@vtuber/ui/components/form";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { cn } from "@vtuber/ui/lib/utils";
import { ChevronDown, Send } from "lucide-react";
import { Dispatch, SetStateAction, useState } from "react";
import { useForm } from "react-hook-form";

type Props = {
  eventId: bigint;
  parentId?: bigint;
  className?: string;
  refetch: (options?: RefetchOptions) => Promise<any>;
  setShowReplies?: Dispatch<SetStateAction<boolean>>;
};

export const EventCommentForm = ({
  eventId,
  parentId,
  className,
  refetch,
  setShowReplies,
}: Props) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const { session, hasVtuberProfile } = useAuth();
  const form = useForm({
    defaultValues: {
      comment: "",
      asVtuber: false,
      parentId,
    },
  });
  const isVtuber = form.watch("asVtuber");
  const comment = form.watch("comment");

  const mutation = useMutation(EventCommentService.method.addEventComment, {
    onSuccess: () => {
      refetch();
      setShowReplies && setShowReplies(true);
      form.reset({
        asVtuber: isVtuber,
        parentId,
      });
    },
    onError: (err) => {
      handleConnectError(err, form);
    },
  });

  const onComment = form.handleSubmit((data) => {
    if (!session) {
      router.navigate({ to: "/login" });
      return;
    }
    mutation.mutate({
      content: data.comment,
      eventId,
      asVtuber: data.asVtuber,
      parentId: data.parentId,
    });
  });

  return (
    <Form {...form}>
      <form
        onSubmit={onComment}
        className={cn("flex items-start gap-3", className)}>
        <DropdownMenu
          open={hasVtuberProfile ? open : false}
          onOpenChange={setOpen}>
          <DropdownMenuTrigger className="w-12 h-12 relative">
            <Avatar
              className=" h-full w-full border border-border bg-muted"
              src={session?.user?.image}
              alt={session?.user?.fullName}
              fallback={session?.user?.fullName}
            />
            <div className="absolute bottom-0.5 right-0 rounded-full bg-border border border-background flex items-center justify-center w-5 h-5">
              <ChevronDown
                size={20}
                className="text-muted-foreground"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              className={isVtuber ? "bg-secondary" : "bg-transparent"}
              onClick={() => form.setValue("asVtuber", true)}>
              Comment as {session?.vtuber?.displayName || "Vtuber"}
            </DropdownMenuItem>
            <DropdownMenuItem
              className={!isVtuber ? "bg-secondary" : "bg-transparent"}
              onClick={() => form.setValue("asVtuber", false)}>
              Comment as {session?.user?.fullName}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <div className="flex-1 space-y-3 relative">
          <TextAreaInput
            className="bg-border pr-24 h-auto overflow-hidden min-h-[40px] pt-4 pb-0 border-none sm:placeholder:text-base text-sm focus-visible:ring-0 rounded-full focus-visible:ring-transparent shadow-none resize-none"
            onInput={(e) => {
              e.currentTarget.style.height = "auto";
              e.currentTarget.style.height = `${e.currentTarget.scrollHeight}px`;
              e.currentTarget.style.borderRadius = "24px";
              e.currentTarget.style.paddingBottom = "0.5rem";
            }}
            name="comment"
            control={form.control}
            placeholder={
              session
                ? `comment as ${isVtuber ? session?.vtuber?.displayName || "Vtuber" : session.user?.fullName}`
                : "Login to comment"
            }
          />
          <Button
            disabled={!session ? false : !comment || mutation.isPending}
            className={
              "rounded-full shadow-none absolute transition-all duration-100 ease-in-out right-2 z-20 bottom-2"
            }
            variant={session ? "secondary" : "default"}
            type="submit"
            loading={mutation.isPending}>
            {!session ? (
              "Login"
            ) : (
              <Send
                size={24}
                className="text-blue-700"
              />
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};
