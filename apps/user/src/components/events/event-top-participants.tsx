import { useLanguage } from "@vtuber/language/hooks";
import { EventParticipantVtuber } from "@vtuber/services/events";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { FirstBadge } from "@vtuber/ui/components/icons/first-badge";
import { SecondBadge } from "@vtuber/ui/components/icons/second-badge";
import { ThirdBadge } from "@vtuber/ui/components/icons/third-badge";
import { Image } from "@vtuber/ui/components/image";
import { cn } from "@vtuber/ui/lib/utils";

interface Props {
  eventTitle?: string;
  topParticipants?: EventParticipantVtuber[];
}

export const EventTopParticipants = ({
  eventTitle,
  topParticipants,
}: Props) => {
  const { getText, language } = useLanguage();

  if (!topParticipants || topParticipants.length === 0)
    return (
      <div className="text-2xl text-center">
        {getText("no_participating_vtubers_for_event")} {eventTitle}
      </div>
    );

  return (
    <div
      className={
        "grid grid-cols-12 items-center md:gap-x-6 gap-x-[13px] md:gap-y-8 gap-y-7"
      }>
      {topParticipants.map((tp, index) => (
        <section
          className={cn(
            "grid",
            index === 0
              ? "col-span-12 gap-y-8"
              : index < 3
                ? "md:col-span-6 col-span-12 gap-y-3"
                : "lg:col-span-3 md:col-span-4 col-span-6 gap-y-[10px]",
          )}
          key={tp.id}>
          {index === 0 ? (
            <div className="flex items-center md:flex-nowrap flex-wrap md:gap-y-0 gap-y-8 justify-center gap-x-8">
              <FirstBadge className="md:h-[128px] md:w-[139px] h-[104px] w-[113px]" />
              <div className="flex flex-col items-center">
                <h6 className="md:text-[35px] text-[28px] font-bold text-transparent bg-clip-text bg-gradient-gold-linear capitalize">
                  {tp.name}
                </h6>
                <div className="bg-clip-text text-transparent bg-gradient-gold-linear">
                  {language === "ja" && (
                    <span className="text-xs font-medium">投票数</span>
                  )}
                  <span className="font-bold text-[29px] font-montserrat">
                    {tp.voteCount}
                  </span>
                  <span className="text-xs font-medium">
                    {language === "ja" ? "投票" : " votes"}
                  </span>
                </div>
              </div>
            </div>
          ) : index < 3 ? (
            <div className="flex items-center justify-center gap-x-8">
              {index === 1 ? <SecondBadge /> : <ThirdBadge />}
              <div>
                <h6
                  className={cn(
                    "md:text-[26px] text-[22px] font-bold text-transparent capitalize bg-clip-text ",
                    index === 1
                      ? "bg-gradient-silver-linear"
                      : "bg-gradient-blond-linear",
                  )}>
                  {tp.name.split(" ")[0]}
                </h6>
                <div
                  className={cn(
                    "bg-clip-text text-transparent text-center",
                    index === 1
                      ? "bg-gradient-silver-linear"
                      : "bg-gradient-blond-linear",
                  )}>
                  {language === "ja" && (
                    <span className="text-xs font-medium">投票数</span>
                  )}
                  <span className="font-bold text-[29px] font-montserrat">
                    {tp.voteCount}
                  </span>
                  <span className="text-xs font-medium">
                    {language === "ja" ? "投票" : " votes"}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-start justify-center md:gap-x-[10px] gap-x-1">
              <div className="size-7 flex items-center justify-center bg-[#4E4E4E] rounded-full font-montserrat font-semibold text-[11px]">
                {index + 1}
              </div>
              <div className="flex flex-col items-center gap-y-[10px] md:flex-1">
                <h6 className="text-xs font-bold text-font capitalize">
                  {tp.name}
                </h6>
                <p className="text-[10px] font-medium text-font text-center">
                  {language === "ja" && "投票数"}
                  <span className="font-semibold font-montserrat text-lg">
                    {tp.voteCount}
                  </span>
                  {language === "ja" ? "投票" : " votes"}
                </p>
              </div>
            </div>
          )}
          <AspectRatio
            ratio={index === 0 ? 704 / 394 : index < 3 ? 340 / 191 : 158 / 89}>
            <Image
              src={tp.image}
              alt={tp.name}
              className="size-full rounded-10"
            />
          </AspectRatio>
        </section>
      ))}
    </div>
  );
};
