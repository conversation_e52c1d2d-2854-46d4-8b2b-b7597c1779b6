import { EventParticipation } from "@vtuber/services/events";
import React, { createContext, useContext, useState } from "react";

export type VoteType = "special" | "daily";

type EventVotingContextType = {
  eventParticipant?: EventParticipation;
  step: number;
  setStep: (s: number) => void;
  onNextStep: () => void;
  onPrevStep: () => void;
  onClose: () => void;
  opened: boolean;
  setOpened: (o: boolean) => void;
  voteType: VoteType | undefined;
  setVoteType: React.Dispatch<React.SetStateAction<VoteType | undefined>>;
  voteAmount: number;
  setVoteAmount: React.Dispatch<React.SetStateAction<number>>;
};

const EventVotingContext = createContext<EventVotingContextType>({
  step: 0,
  setStep: () => {},
  onNextStep: () => {},
  onPrevStep: () => {},
  onClose: () => {},
  opened: false,
  setOpened: () => {},
  voteType: undefined,
  setVoteType: () => {},
  voteAmount: 0,
  setVoteAmount: () => {},
});

interface Props {
  data?: EventParticipation;
  children: React.ReactNode;
}

export const EventVotingModalProvider = ({ data, children }: Props) => {
  const [step, setStep] = useState(0);
  const [opened, setOpened] = useState(false);
  const [voteType, setVoteType] = useState<VoteType | undefined>();
  const [voteAmount, setVoteAmount] = useState(0);

  const onNextStep = () => {
    setStep((prev) => prev + 1);
  };
  const onPrevStep = () => {
    setStep((prev) => prev - 1);
  };
  const onClose = () => {
    setOpened(false);
  };
  return (
    <EventVotingContext.Provider
      value={{
        eventParticipant: data,
        step,
        setStep,
        onNextStep,
        onPrevStep,
        onClose,
        opened,
        setOpened,
        setVoteAmount,
        setVoteType,
        voteAmount,
        voteType,
      }}>
      {children}
    </EventVotingContext.Provider>
  );
};

export const useEventVotingModal = () => {
  const context = useContext(EventVotingContext);
  if (!context) {
    throw new Error(
      "useEventModal must be used within a EventVotingModalProvider",
    );
  }
  return context;
};
