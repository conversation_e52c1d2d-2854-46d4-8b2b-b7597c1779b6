import { Button } from "@vtuber/ui/components/button";
import { VotingCompleteIcon } from "@vtuber/ui/components/icons/voting-complete-icon";
import { Separator } from "@vtuber/ui/components/separator";
import { X } from "lucide-react";
import { useEventVotingModal } from "./event-voting-modal-provider";

export const EventVotingFinalStep = () => {
  const { onClose, voteAmount, voteType } = useEventVotingModal();
  return (
    <div>
      <div className="flex flex-row items-center justify-end p-3 border-b-4 border-b-[#302e41]">
        <button
          onClick={onClose}
          className="rounded-full size-10 hover:bg-white/10 flex items-center justify-center">
          <X className="!size-[27px]" />
        </button>
      </div>
      <div className="grid py-4 gap-y-4 px-8 text-font">
        <h3 className="text-center font-bold text-[22px]">
          投票が完了しました
        </h3>
        <h6 className="font-bold text-center leading-[200%]">
          以下の投票数にて投票済みです。 <br />
          投票していただきありがとうございました。
        </h6>
        <div className="flex justify-center">
          <VotingCompleteIcon />
        </div>
        <section className="text-font">
          <Separator />
          {voteType === "special" ? (
            <div className="flex items-center justify-between px-4 py-[23px]">
              <p className="text-sm font-medium">スペシャル投票券</p>
              <p className="font-bold text-[22px]">
                {voteAmount}
                <span className="text-sm font-medium">券</span>{" "}
              </p>
            </div>
          ) : (
            <div className="flex items-center justify-between px-4 py-[23px]">
              <p className="text-sm font-medium">デイリー投票券</p>
              <p className="font-bold text-[22px]">
                {voteAmount}
                <span className="text-sm font-medium">券</span>{" "}
              </p>
            </div>
          )}
          <Separator />
        </section>
        <div className="px-8">
          <Button
            onClick={onClose}
            variant={"tertiary"}
            className="w-full font-bold text-white"
            size={"xl"}>
            閉じる
          </Button>
        </div>
      </div>
    </div>
  );
};
