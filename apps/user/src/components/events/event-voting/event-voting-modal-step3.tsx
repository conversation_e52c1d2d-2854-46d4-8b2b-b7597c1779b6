import { useMutation } from "@connectrpc/connect-query";
import { useQueryClient } from "@tanstack/react-query";
import { EventVoteService } from "@vtuber/services/events";
import { Button } from "@vtuber/ui/components/button";
import { Input } from "@vtuber/ui/components/input";
import { Label } from "@vtuber/ui/components/label";
import { RadioGroup, RadioGroupItem } from "@vtuber/ui/components/radio-group";
import { Separator } from "@vtuber/ui/components/separator";
import { Check, ChevronLeft, X } from "lucide-react";
import { motion } from "motion/react";
import { useState } from "react";
import { toast } from "sonner";
import { useEventVotingModal, VoteType } from "./event-voting-modal-provider";

export const EventVotingStep3 = ({
  canVoteToday,
  points,
  eventParticipationId,
}: {
  canVoteToday?: boolean;
  points: number;
  eventParticipationId: bigint;
}) => {
  const queryClient = useQueryClient();
  const {
    onClose,
    onNextStep,
    onPrevStep,
    setVoteType,
    setVoteAmount,
    voteAmount,
    voteType,
  } = useEventVotingModal();
  const [pointType, setPointType] = useState<"all" | "partial">();
  const { mutate, isPending } = useMutation(EventVoteService.method.addVote, {
    onSuccess: (data) => {
      toast.success(data.message);
      queryClient.invalidateQueries({ queryKey: ["daily-voting"] });
      queryClient.invalidateQueries({ queryKey: ["points"] });
      onNextStep();
    },
    onError: (err) => {
      toast.error(err.rawMessage);
    },
  });

  const onVote = () => {
    if (voteType === "special" && !pointType) {
      toast.error("Please select either all or partial points");
      return;
    }
    if (voteAmount > points && voteType !== "daily") {
      toast.error("can't use more than available points");
      return;
    }
    if (pointType === "partial" && voteAmount == 0) {
      toast.error("please enter voting points");
      return;
    }

    mutate({
      eventParticipationId,
      fromDailyPoint: voteType === "daily",
      points: Math.floor(voteAmount),
    });
  };

  return (
    <div>
      <motion.div
        initial={{
          translateY: 12,
          opacity: 0,
          transition: {
            ease: "easeIn",
            bounce: 0,
          },
        }}
        animate={{
          translateY: 0,
          opacity: 1,
        }}
        className="flex flex-row items-center justify-between p-3 text-font border-b-4 border-b-[#302e41]">
        <button
          onClick={onPrevStep}
          className="rounded-full size-10 hover:bg-white/10 flex items-center justify-center">
          <ChevronLeft className="!size-[22px]" />
        </button>
        <h6 className="font-bold text-xl">投票券を選択する</h6>
        <button
          onClick={onClose}
          className="rounded-full size-10 hover:bg-white/10 flex items-center justify-center">
          <X className="!size-[27px]" />
        </button>
      </motion.div>
      <RadioGroup
        defaultValue={voteType}
        onValueChange={(e) => {
          setVoteType(e as VoteType);
          if (e === "daily") {
            setVoteAmount(1);
          }
        }}
        className="px-8 pb-20 grid gap-y-6">
        <div className="text-font grid gap-y-2 py-8">
          <div className="flex items-center gap-x-[14px]">
            <RadioGroupItem
              value={"special"}
              id="special"
              className="size-5 border-[#7A7A7A] rounded-none data-[state=checked]:border-primary data-[state=checked]:bg-transparent group"
              icon={<Check className="size-3.5" />}
            />

            <Label
              htmlFor="special"
              className="text-sm font-medium">
              スペシャル投票権を利用する
            </Label>
          </div>
          <RadioGroup
            disabled={voteType !== "special"}
            value={pointType}
            onValueChange={(e) => {
              setPointType(e as "all" | "partial");
              if (e === "all") {
                setVoteAmount(points);
              }
            }}
            className="grid gap-y-2 pl-10">
            <section className="grid gap-y-4 text-font">
              <div className="flex items-center gap-x-[14px]">
                <RadioGroupItem
                  className="border-[#7A7A7A]"
                  value="all"
                  id="all"
                />
                <Label htmlFor="all">
                  利用可能なスペシャル投票券を⁨⁩すべて利用する
                </Label>
              </div>
              <p className="text-right text-[22px] font-bold">
                {points}
                <span className="font-medium text-sm">券</span>
              </p>
            </section>
            <div className="flex items-start gap-x-[14px]">
              <RadioGroupItem
                value="partial"
                id="partial"
                className="border-[#7A7A7A] mt-1"
              />
              <div className="space-y-2">
                <Label htmlFor="partial">
                  スペシャル投票券を⁨⁩一部利用する
                </Label>
                <div className="flex items-center gap-x-2">
                  <Input
                    type="number"
                    value={
                      pointType === "partial" ? voteAmount.toString() : "0"
                    }
                    onChange={(e) => {
                      const intVal = Math.floor(Number(e.target.value));
                      if (!isNaN(intVal)) {
                        setVoteAmount(intVal);
                      }
                    }}
                    className="border-white rounded-none bg-[#2C2A37] h-[31px]"
                    disabled={pointType !== "partial"}
                  />{" "}
                  <small className="text-xs font-medium">券</small>
                </div>
              </div>
            </div>
          </RadioGroup>
        </div>
        <div className="py-8 space-y-8">
          <Separator className="bg-font" />
          {canVoteToday ? (
            <section className="flex items-center justify-between">
              <div className="flex items-center gap-x-3">
                <RadioGroupItem
                  id="daily"
                  value={"daily"}
                  className="size-5 border-[#7A7A7A] rounded-none data-[state=checked]:border-primary data-[state=checked]:bg-transparent group"
                  icon={<Check className="size-3.5" />}
                />
                <Label
                  htmlFor="daily"
                  className="text-sm font-medium text-font">
                  デイリー投票券を利用する
                </Label>
              </div>
              <p className="text-[22px] font-bold">
                1<span className="font-medium text-sm">券</span>
              </p>
            </section>
          ) : (
            <div className="capitalize text-center text-sm text-font text-blue-500">
              Daily voting not available because you have already voted for
              today.
            </div>
          )}
          <Separator className="bg-font" />
        </div>
        <Button
          size={"xl"}
          onClick={onVote}
          loading={isPending}
          className="font-bold rounded-xs"
          variant={"outline"}>
          選択した投票券で投票する
        </Button>
      </RadioGroup>
    </div>
  );
};
