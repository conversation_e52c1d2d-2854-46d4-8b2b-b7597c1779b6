import { Link } from "@tanstack/react-router";
import { Avatar } from "@vtuber/ui/components/avatar";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { DiscordIcon } from "@vtuber/ui/components/icons/discord-icon";
import { InstagramIcon } from "@vtuber/ui/components/icons/instagram-icon";
import { TikTokIcon } from "@vtuber/ui/components/icons/tittok-icon";
import { TwitchIcon } from "@vtuber/ui/components/icons/twitch-icon";
import { XIcon } from "@vtuber/ui/components/icons/x-icon";
import { YoutubeIcon } from "@vtuber/ui/components/icons/youtube-icon";
import { X } from "lucide-react";
import { useEventVotingModal } from "./event-voting-modal-provider";

const externalLinks = [
  {
    href: "",
    icon: <XIcon />,
  },
  {
    href: "",
    icon: <TikTokIcon />,
  },
  {
    href: "",
    icon: <InstagramIcon />,
  },
  {
    href: "",
    icon: <YoutubeIcon className="size-6 [&>path]:fill-white" />,
  },
  {
    href: "",
    icon: <DiscordIcon />,
  },
  {
    href: "",
    icon: <TwitchIcon />,
  },
];

export const EventVotingStep1 = () => {
  const { onClose, eventParticipant, onNextStep } = useEventVotingModal();
  return (
    <div>
      <header className="flex justify-end px-8 py-6 border-b-4 border-b-[#302e41]">
        <button onClick={onClose}>
          <X />
        </button>
      </header>
      <div className="px-8 py-6 grid gap-y-6">
        <section className="flex flex-col items-center gap-y-5">
          <Avatar
            className="size-[156px]"
            src={eventParticipant?.vtuber?.image}
            fallback={eventParticipant?.vtuber?.name || ""}
          />
          <p className="text-font text-[22px] font-medium">
            {eventParticipant?.vtuber?.name}
          </p>
        </section>
        <section className="grid gap-y-2">
          <p className="text-sm text-font">
            {eventParticipant?.vtuber?.introduction}
          </p>
          <Link
            to="/vtuber/$id"
            params={{
              id: eventParticipant?.vtuber?.id.toString() || "",
            }}
            className="text-tertiary font-medium text-sm">
            Vtuberのプロフィールを見る
          </Link>
        </section>
        <section className={"flex items-center gap-x-3"}>
          {externalLinks.map((link, i) => (
            <a
              key={i}
              className="size-[38px] border border-font rounded-full flex items-center justify-center hover:bg-white/10"
              href={link.href}
              target="_blank"
              rel="noreferrer">
              {link.icon}
            </a>
          ))}
        </section>
        <Button
          variant={"tertiary"}
          size={"xl"}
          className="text-white font-bold rounded-xs"
          onClick={onNextStep}>
          投票して応援する
        </Button>
      </div>
    </div>
  );
};
