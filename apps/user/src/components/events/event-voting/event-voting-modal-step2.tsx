import { Button } from "@vtuber/ui/components/button";
import { TicketIcon } from "@vtuber/ui/components/icons/ticket-icon";
import { Separator } from "@vtuber/ui/components/separator";
import { ChevronLeft, X } from "lucide-react";
import { motion } from "motion/react";
import { useEventVotingModal } from "./event-voting-modal-provider";

export const EventVotingStep2 = ({ points }: { points: number }) => {
  const { onPrevStep, onClose, onNextStep } = useEventVotingModal();

  return (
    <div>
      <motion.div
        initial={{
          translateY: 12,
          opacity: 0,
          transition: {
            ease: "easeIn",
            bounce: 0,
          },
        }}
        animate={{
          translateY: 0,
          opacity: 1,
        }}
        className="flex flex-row items-center justify-between p-3 text-font border-b-4 border-b-[#302e41]">
        <button
          onClick={onPrevStep}
          className="rounded-full size-10 hover:bg-white/10 flex items-center justify-center">
          <ChevronLeft className="!size-[22px]" />
        </button>
        <h6 className="font-bold text-xl">投票券を選択する</h6>
        <button
          onClick={onClose}
          className="rounded-full size-10 hover:bg-white/10 flex items-center justify-center">
          <X className="!size-[27px]" />
        </button>
      </motion.div>
      <motion.div
        initial={{
          translateX: 12,
          opacity: 0,
          transition: {
            ease: "easeIn",
            bounce: 0,
          },
        }}
        animate={{
          translateX: 0,
          opacity: 1,
        }}
        className="px-8 py-6 grid gap-y-6">
        <div className="bg-gradient-5 rounded-[8px] py-[15px] px-4 grid gap-y-[25px]">
          <section className="flex flex-col items-center text-center text-font gap-y-[9px]">
            <h6 className="text-xl font-bold">所有投票券</h6>
            <div className="flex items-center justify-center gap-x-3">
              <TicketIcon />
              <p className="text-[22px] font-bold">
                {points}
                <span className="text-sm font-medium">券</span>
              </p>
            </div>
          </section>
          <section className="text-font grid gap-y-[9px]">
            <div className="flex items-center justify-between">
              <p className="text-sm font-bold">スペシャル投票券</p>
              <p>{points}券</p>
            </div>
            <Separator className="bg-font" />
            <div className="flex items-center justify-between">
              <p className="text-sm font-bold">デイリー投票券</p>
              <p className="text-[22px] font-bold">
                1<span className="text-sm font-medium">券</span>
              </p>
            </div>
          </section>
        </div>
        <div className="px-6 w-full">
          <Button
            size={"xl"}
            onClick={onNextStep}
            className="rounded-xs w-full"
            variant={"outline"}>
            投票券を選択する
          </Button>
        </div>
        <ul className="list-disc list-inside text-font text-xs font-medium space-y-1">
          <li>デイリー投票券は、1日1票のみ投票することができます。</li>
          <li>
            スペシャル投票券から任意の投票数をお選びいただき投票することができます。
          </li>
          <li> 例：500円ごと＝1票</li>
        </ul>
      </motion.div>
    </div>
  );
};
