import { useLanguage } from "@vtuber/language/hooks";
import { EventParticipation } from "@vtuber/services/events";
import { BorderedHeading } from "@vtuber/ui/components/bordered-heading";
import { Button } from "@vtuber/ui/components/button";
import { Card } from "@vtuber/ui/components/card";
import { EventParticipatingVtuberCard } from "./event-participating-vtuber-card";

export const EventParticipatingVtubers = ({
  eventParticipants,
}: {
  eventParticipants?: EventParticipation[];
}) => {
  const { getText } = useLanguage();
  return (
    <Card
      className="shadow-none bg-sub border-none rounded-10 py-[39px] px-8 grid gap-y-4 max-w-[368px] ml-auto"
      style={{
        filter: "drop-shadow(0px 0px 26px rgba(126, 255, 254, 0.2))",
      }}>
      <BorderedHeading
        headingType="h3"
        borderOrientation="vertical">
        {getText("participating_vtubers")}
      </BorderedHeading>
      <h6 className="text-center font-bold text-font text-xl">
        {getText("rearrange_vtubers")}
      </h6>
      <section className="flex items-center justify-between">
        <Button
          size={"sm"}
          variant={"white-outline"}
          className="text-xs font-medium">
          50音順
        </Button>
        <Button
          size={"sm"}
          className="text-xs font-medium"
          variant={"golden"}>
          エントリー順
        </Button>
        <Button
          size={"sm"}
          className="text-xs font-medium"
          variant={"golden"}>
          カテゴリ1
        </Button>
      </section>
      <section className="grid gap-y-8">
        {eventParticipants?.length === 0 ? (
          <div className="pt-12 text-center text-xl">
            {getText("no_participating_vtubers")}
          </div>
        ) : (
          eventParticipants?.map((e) => (
            <EventParticipatingVtuberCard
              key={e.id}
              data={e}
            />
          ))
        )}
      </section>
    </Card>
  );
};
