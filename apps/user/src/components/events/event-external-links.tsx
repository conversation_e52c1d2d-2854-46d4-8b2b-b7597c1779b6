import { Event } from "@vtuber/services/events";
import { DiscordIcon } from "@vtuber/ui/components/icons/discord-icon";
import { InstagramIcon } from "@vtuber/ui/components/icons/instagram-icon";
import { TikTokIcon } from "@vtuber/ui/components/icons/tittok-icon";
import { TwitchIcon } from "@vtuber/ui/components/icons/twitch-icon";
import { XIcon } from "@vtuber/ui/components/icons/x-icon";
import { YoutubeIcon } from "@vtuber/ui/components/icons/youtube-icon";

const links = [
  {
    href: "",
    icon: <XIcon className="text-font" />,
  },
  {
    href: "",
    icon: <TikTokIcon className="text-font" />,
  },
  {
    href: "",
    icon: <InstagramIcon className="text-font" />,
  },
  {
    href: "",
    icon: <YoutubeIcon className="[&>path]:fill-font size-6" />,
  },
  {
    href: "",
    icon: <DiscordIcon className="text-font" />,
  },
  {
    href: "",
    icon: <TwitchIcon className="text-font" />,
  },
];

export const EventExternalLinks = ({ event }: { event: Event }) => {
  return (
    <div className="bg-sub py-[10px] flex items-center justify-center gap-x-3">
      {links.map((link, i) => (
        <a
          href={link.href}
          key={i}
          target="_blank"
          rel="noreferrer"
          className="size-[38px] rounded-full flex items-center justify-center border border-font hover:bg-white/10 text-font">
          {link.icon}
        </a>
      ))}
    </div>
  );
};
