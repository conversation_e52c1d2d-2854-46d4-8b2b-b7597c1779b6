import { useLanguage } from "@vtuber/language/hooks";
import { Event } from "@vtuber/services/events";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { HandScrollIcon } from "@vtuber/ui/components/icons/hand-scroll-icon";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";

export const EventParticipationRequirements = ({ event }: { event: Event }) => {
  const { getText } = useLanguage();
  return (
    <div className="grid group gap-y-8">
      <DisplayTag text="event_participation_requirements" />
      <div className="bg-[#2C2A37] border border-[#A9A9A9] rounded-[24px] relative">
        <ScrollArea className="max-h-[768px] flex flex-col">
          <div className="absolute bottom-0 w-full bg-gradient-to-b from-transparent to-black/60 h-6 rounded-b-[24px]" />
          <div className="text-font grid gap-y-5 p-6 ">
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-[#686868] rounded-10 group-hover:hidden py-[18px] px-2 z-10 w-[138px] sm:flex hidden items-center justify-center">
              <div className="flex flex-col items-center gap-3">
                <HandScrollIcon />
                <small className="text-[10px] font-medium text-white">
                  {getText("vertical_scroll")}
                </small>
              </div>
            </div>
            <MarkDown markdown={event.requirements} />
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
