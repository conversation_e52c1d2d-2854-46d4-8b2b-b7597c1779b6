import {
  Await,
  getRoute<PERSON><PERSON>,
  useNavigate,
  useSearch,
} from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Event } from "@vtuber/services/events";
import { Button } from "@vtuber/ui/components/button";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { EventParticipantsSkeleton } from "../skeletons/event-participants-skeleton";
import { EventTopParticipantsSkeleton } from "../skeletons/event-top-participants-skeleton";
import { EventParticipants } from "./event-participants";
import { EventTopParticipants } from "./event-top-participants";

export const EventResultsTab = ({ event }: { event?: Event }) => {
  const Route = getRouteApi("/_app/event/$id/");
  const { eventParticipantsPromise, topParticipantsPromise } =
    Route.useLoaderData();
  const { getText, language } = useLanguage();
  const { tab } = useSearch({ from: "/_app/event/$id/" });
  const [tabValue, setTabValue] = useState(tab || "results");
  const navigate = useNavigate();

  useEffect(() => {
    if (tab) {
      setTabValue(tab);
    }
  }, [tab]);

  return (
    <div className="grid gap-y-10">
      <DisplayTag text="announcement_of_event_results" />
      <div className="bg-[#0B0B0B] rounded-[18px] py-12 px-8 relative overflow-hidden">
        {tabValue === "results" && (
          <div className="absolute inset-0 bg-[url('https://cdn.v-sai.com/assets/event_results_bg.png')] bg-no-repeat bg-center bg-cover z-10 opacity-15">
            <div className="size-full bg-gradient-to-b from-black/30 to-black/90" />
          </div>
        )}
        <Tabs
          className="relative z-30"
          variant={"golden"}
          value={tabValue}
          onValueChange={(val) => {
            setTabValue(val);
            navigate({
              // @ts-ignore
              search: (prev) => ({
                ...prev,
                tab: val,
              }),
              resetScroll: false,
            });
          }}>
          <TabsList className="grid grid-cols-12 gap-x-2 md:gap-y-0 gap-y-2 w-full">
            <TabsTrigger
              value="results"
              className="text-center md:col-span-4 col-span-6">
              {language === "ja" ? (
                <div>TOP イベント大賞</div>
              ) : (
                <div>
                  Top <br /> Participants
                </div>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="offering_rewards"
              className="md:col-span-4 col-span-6">
              <div className="flex items-center text-balance whitespace-pre-wrap sm:gap-x-3">
                <AlertCircle className="text-red01 sm:block hidden" />
                {getText("v_offering_rewards")}
              </div>
            </TabsTrigger>

            <TabsTrigger
              value="participating_vtubers"
              className="md:col-span-4 col-span-12">
              {getText("participating_vtubers")}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="results">
            <div className="pt-14 grid gap-y-14 relative z-30">
              <Await
                promise={topParticipantsPromise}
                fallback={<EventTopParticipantsSkeleton />}
                children={([participants]) => (
                  <EventTopParticipants
                    topParticipants={participants?.data}
                    eventTitle={event?.title}
                  />
                )}
              />
            </div>
          </TabsContent>
          <TabsContent value="offering_rewards">
            <article className="pt-14 grid gap-y-14">
              Vtuber Offering Rewards
            </article>
          </TabsContent>
          <TabsContent value="participating_vtubers">
            <div className="pt-14 grid gap-y-14">
              <section className="flex flex-col gap-y-4 items-center">
                <h3 className="font-bold text-font text-xl">
                  {getText("rearrange_vtubers")}
                </h3>
                <div className="flex items-center justify-center gap-x-3">
                  <Button
                    variant={"white-outline"}
                    className="text-xs font-medium">
                    50音順
                  </Button>
                  <Button
                    className="text-xs font-medium"
                    variant={"golden"}>
                    エントリー順
                  </Button>
                  <Button
                    className="text-xs font-medium"
                    variant={"golden"}>
                    カテゴリ1
                  </Button>
                </div>
              </section>
              <Await
                promise={eventParticipantsPromise}
                fallback={<EventParticipantsSkeleton />}
                children={([participants]) => (
                  <EventParticipants events={participants?.data} />
                )}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
