import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useCategories } from "@vtuber/auth/hooks";
import { Event } from "@vtuber/services/events";
import { RemainingDays } from "@vtuber/ui/components/remaining-days";
import { Tag } from "@vtuber/ui/components/tag";

const tags = ["イラスト", "Live2D", "3Dモデル", "プレゼント企画"];

export const EventDetailsHeader = ({ event }: { event: Event }) => {
  const { getCategoryById } = useCategories();
  return (
    <header className="gap-y-6 grid">
      <h1 className="sm:text-[26px] text-2xl font-bold text-font">
        {event.title}
      </h1>
      <section className="flex md:items-end flex-wrap gap-x-5">
        <p className="font-medium text-font">
          {timestampDate(event.startDate!).toLocaleDateString()} -{" "}
          {timestampDate(event.endDate!).toLocaleDateString()}
        </p>
        {event.categoryId && (
          <Tag variant={"success"}>
            {getCategoryById(event.categoryId)?.name}
          </Tag>
        )}
        <RemainingDays
          className="text-[17px]"
          daysClassname="text-[34px]"
          endDate={event.endDate!}
          startDate={event.startDate!}
        />
      </section>
      <section className="flex items-center md:gap-5 gap-2 flex-wrap">
        {tags.map((t) => (
          <Tag
            className="h-8 px-6"
            variant={"outline-white"}
            key={t}>
            {t}
          </Tag>
        ))}
      </section>
    </header>
  );
};
