import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { EventParticipation } from "@vtuber/services/events";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card } from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { ChevronRight } from "lucide-react";
import { EventVotingModal } from "./event-voting-modal";

export const EventParticipants = ({
  events,
}: {
  events?: EventParticipation[];
}) => {
  const { getText, language } = useLanguage();

  if (!events || events.length === 0)
    return (
      <div className="h-44 flex items-center justify-center text-center text-xl">
        {getText("no_participating_vtubers")}
      </div>
    );
  return (
    <section className="grid items-end lg:grid-cols-4 md:grid-cols-3 grid-cols-2 gap-x-6 gap-y-10">
      {events?.map((ep) => (
        <Card
          key={ep.id}
          className="bg-transparent shadow-none border-none grid gap-y-[10px]">
          <h6 className="text-font text-lg font-bold">{ep.vtuber?.name}</h6>
          <p className="text-xs font-medium text-font">
            {language === "ja" && getText("number_of_votes")}{" "}
            <span className="text-[22px] font-semibold font-montserrat">
              {ep.voteCount}
            </span>{" "}
            {getText("votes")}
          </p>
          <AspectRatio ratio={157 / 88}>
            <Image
              src={ep.vtuber?.image}
              className="size-full rounded-10"
              alt={ep.vtuber?.name}
            />
          </AspectRatio>
          <Link
            to="/vtuber/$id"
            params={{
              id: ep.vtuber?.id.toString() || "",
            }}
            className="text-primary text-xs sm:pr-2 font-bold flex items-center sm:justify-between justify-center sm:no-underline underline">
            {getText("view_profile")}{" "}
            <ChevronRight className="size-4 sm:block hidden" />
          </Link>
          <EventVotingModal
            size={"sm"}
            eventId={ep.eventId}
            eventParticipationId={ep.id}
            className="bg-background"
          />
        </Card>
      ))}
    </section>
  );
};
