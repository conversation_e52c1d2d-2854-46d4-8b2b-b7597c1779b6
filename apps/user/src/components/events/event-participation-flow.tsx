import { Event } from "@vtuber/services/events";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { MarkDown } from "@vtuber/ui/components/markdown";

export const EventParticipationFlow = ({ event }: { event: Event }) => {
  return (
    <div className="grid gap-y-8">
      <DisplayTag text="particiaption_flow" />
      <MarkDown
        className="text-font"
        markdown={event.participationFlow}
      />
    </div>
  );
};
