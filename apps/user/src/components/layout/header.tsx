import { Link, useLocation } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { LanguageSelector } from "@vtuber/ui/components/language-selector";
import { Logo } from "@vtuber/ui/components/logo";
import { cn } from "@vtuber/ui/lib/utils";
import { useEffect, useState } from "react";
import { HeaderLinks } from "./header-links";
import { HeaderSearchBar } from "./header-search-bar";
import { MobileSidebar } from "./mobile-sidebar";
import { NotificationDropdown } from "./notification/notification-dropdown";
import { PointsBadge } from "./points-badge";
import { UserDropDown } from "./user-dropdown";

export const Header = ({ className }: { className?: string }) => {
  const { pathname } = useLocation();
  const { session } = useAuth();
  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const handleScroll = (e: Event) => {
      const scrollPosition = (e.target as Document).scrollingElement?.scrollTop;
      setScrollPosition(scrollPosition || 0);
    };

    document.addEventListener("scroll", handleScroll);

    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header
      className={cn(
        "w-full flex items-center justify-between sm:px-10 px-4 h-20 lg:bg-transparent lg:shadow-none shadow-lg",
        pathname === "/" && scrollPosition > 831
          ? "bg-background/30 backdrop-blur w-full top-0 z-50 fixed animate-slide-in-down"
          : "absolute top-0 w-full z-50",
        pathname !== "/" &&
          "fixed top-0 z-50 bg-background/30 backdrop-blur-[8px]",
        className,
      )}>
      <div className="flex items-center lg:gap-x-6">
        <Link to="/">
          <Logo className="sm:w-[112px] sm:h-9 w-[96px] h-[30px]" />
        </Link>
        <HeaderSearchBar className="hidden xl:block" />
      </div>

      <div className="flex xl:hidden items-center sm:gap-6 gap-3">
        <div className="md:pr-8 pr-2">
          <UserDropDown />
        </div>
        <HeaderSearchBar />
        <MobileSidebar />
      </div>

      <section className="items-center gap-x-[22px] xl:flex hidden">
        <HeaderLinks
          className={scrollPosition > 831 ? "text-white" : "text-font"}
        />
        {session && <PointsBadge />}
        <LanguageSelector className="rounded-full size-8 lg:flex hidden items-center justify-center" />
        {session && <NotificationDropdown />}
        <UserDropDown />
      </section>
    </header>
  );
};
