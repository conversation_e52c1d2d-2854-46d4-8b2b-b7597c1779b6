import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { LanguageKey } from "@vtuber/language/types";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { JapaneseFlag } from "@vtuber/ui/components/icons/japanese-flag-icon";
import { MenuIcon } from "@vtuber/ui/components/icons/menu-icon";
import { USFlag } from "@vtuber/ui/components/icons/us-flag-icon";
import { LanguageSelector } from "@vtuber/ui/components/language-selector";
import { Logo } from "@vtuber/ui/components/logo";
import { Separator } from "@vtuber/ui/components/separator";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@vtuber/ui/components/sheet";
import { ChevronDown, ChevronRight, Minus } from "lucide-react";
import { useState } from "react";

export const MobileSidebar = () => {
  const { getText, language } = useLanguage();
  const [opened, setOpened] = useState(false);
  const activeProps = { className: "text-primary" };

  const flags: Record<LanguageKey, React.ReactNode> = {
    en: <USFlag className="size-6" />,
    ja: <JapaneseFlag className="size-6" />,
  };

  return (
    <Sheet
      open={opened}
      onOpenChange={setOpened}>
      <SheetTrigger asChild>
        <button className="px-[10px] py-3 rounded-[40px] border border-white">
          <MenuIcon />
        </button>
      </SheetTrigger>
      <SheetContent
        side={"right"}
        withCloseButton={false}
        className="border-none w-[335px] p-0 space-y-10"
        aria-describedby="mobile-sidebar"
        aria-description="Mobile sidebar">
        <SheetHeader className="sr-only">
          <SheetTitle>Mobile Sidebar</SheetTitle>
        </SheetHeader>
        <nav className="space-y-1 text-sm font-bold text-[#DADADA] px-4 capitalize">
          <Link
            to="/"
            activeProps={activeProps}
            className="py-3">
            {getText("home")}
          </Link>
          <Link
            to="/event"
            activeProps={activeProps}
            className="flex items-center justify-between py-3">
            {getText("events")}{" "}
            <ChevronRight className="size-6 text-tertiary" />
          </Link>
          <Link
            to="/vtuber"
            activeProps={activeProps}
            className="flex items-center justify-between py-3">
            Vtuber <ChevronRight className="size-6 text-tertiary" />
          </Link>
          <Link
            to="/campaign"
            activeProps={activeProps}
            className="flex items-center justify-between py-3">
            {getText("crowdfunding")}
            <ChevronRight className="size-6 text-tertiary" />
          </Link>
          <Link
            to="/faq"
            activeProps={activeProps}
            className="flex items-center justify-between py-3">
            {getText("faq")} <ChevronRight className="size-6 text-tertiary" />
          </Link>
          <Link
            to="/support"
            activeProps={activeProps}
            className="flex items-center justify-between py-3">
            {getText("begginers_guide_users")}{" "}
            <ChevronRight className="size-6 text-tertiary" />
          </Link>
          <LanguageSelector
            className="flex w-full items-center justify-between py-3 group"
            align="end">
            <p>{getText("Select_Language")}</p>
            <div className="flex items-center gap-1">
              {flags[language as LanguageKey]}
              <ChevronDown className="size-6 text-tertiary transition-all ease-in-out duration-200 group-data-[state=open]:-rotate-180" />
            </div>
          </LanguageSelector>
          <div>
            <p className="flex items-center justify-between py-3">
              {getText("event_category")}{" "}
              <Minus className="size-6 text-tertiary" />
            </p>
            <section className="grid grid-cols-2 items-center gap-3 text-xs text-[#B4B4B4]">
              <p className="flex items-center gap-x-1">
                <Minus className="size-6 text-tertiary" />
                ダミーサブメニュー
              </p>
              <p className="flex items-center gap-x-1">
                <Minus className="size-6 text-tertiary" />
                ダミーサブメニュー
              </p>
              <p className="flex items-center gap-x-1">
                <Minus className="size-6 text-tertiary" />
                ダミーサブメニュー
              </p>
            </section>
          </div>
        </nav>
        <Link
          to="/"
          className="flex justify-center">
          <Logo className="h-[42px] w-[132px]" />
        </Link>
        <section className="grid gap-y-8">
          <ExternalLinks
            socialMediaLinks={{
              discord: "https://",
              instagram: "https://",
              youtube: "https://",
              tiktok: "https://",
              twitch: "https://",
              twitter: "https://",
            }}
          />
          <Separator />
          <div className="flex items-center flex-wrap gap-5 px-2 text-xs text-[#747474] justify-center">
            <Link to="/terms">{getText("terms_of_use")}</Link>
            <Link
              activeProps={activeProps}
              to="/transaction-act">
              {getText("transaction_act")}
            </Link>
            <Link
              activeProps={activeProps}
              to="/support">
              {getText("guidelines")}
            </Link>
            <Link to="/privacy">{getText("privacy_policy")}</Link>
            <Link to="/operating-company">{getText("operating_company")}</Link>
            <Link to="/site-map">{getText("sitemap")}</Link>
          </div>
        </section>
        <small className="text-center block text-xs text-[#747474]">
          ©{new Date().getFullYear()} Vsai. All Rights Reserved.
        </small>
      </SheetContent>
    </Sheet>
  );
};
