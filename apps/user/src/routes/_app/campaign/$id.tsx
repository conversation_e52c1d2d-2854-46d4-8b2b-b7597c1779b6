import { createFileRoute, notFound } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { campaignClient } from "@vtuber/services/client";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@vtuber/ui/components/carousel";
import { Container } from "@vtuber/ui/components/container";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { HeaderBreadCrumb } from "@vtuber/ui/components/header-bread-crumb";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { RouteFallback } from "@vtuber/ui/components/route-fallback";
import { z } from "zod";
import { CampaignCard } from "~/components/campaign/campaign-card";
import { CampaignDetailsBanner } from "~/components/campaign/campaign-details-banner";
import { CampaignDetailsCard } from "~/components/campaign/campaign-details-card";
import { CampaignDetailsHeader } from "~/components/campaign/campaign-details-header";
import { CampaignOverviewTabs } from "~/components/campaign/campaign-overview-tabs";
import { CampaignVariantsList } from "~/components/campaign/campaign-variants-list";
import { CampaignVtuberDetails } from "~/components/campaign/campaign-vtuber-details";
import { CallToAction } from "~/components/layout/call-to-action";

export const Route = createFileRoute("/_app/campaign/$id")({
  component: RouteComponent,
  validateSearch: z.object({
    tab: z.string().optional(),
    commentId: z.number().optional(),
  }),
  loader: async ({ params }) => {
    const [campaign, err] = await campaignClient.getCampaignById({
      id: params.id,
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    const [relatedCampaigns] = await campaignClient.getAllCampaigns({
      categoryId: campaign?.data?.categoryId,
      pagination: {
        size: 4,
      },
    });

    return {
      campaign: campaign.data,
      relatedCampaigns: relatedCampaigns?.data,
    };
  },

  staleTime: 5000,
});

function RouteComponent() {
  const { getText } = useLanguage();
  const { campaign, relatedCampaigns } = Route.useLoaderData();
  if (!campaign)
    return (
      <RouteFallback
        className="pt-20"
        data={{
          data: "sorry, we couldn't find the campaign you're looking for.",
        }}
      />
    );

  return (
    <div className="md:pt-28 pt-[92px]">
      <Container className="pb-20">
        <HeaderBreadCrumb className="md:hidden flex" />
        <div className="flex flex-col gap-y-32 md:pt-0 pt-10">
          <div className="grid grid-cols-12 items-start">
            <section className="md:col-span-7 col-span-12 flex flex-col gap-y-10">
              <CampaignDetailsHeader campaign={campaign} />
              <CampaignDetailsBanner campaign={campaign} />
              <MarkDown
                markdown={campaign?.shortDescription}
                className="text-font"
              />
              <ExternalLinks
                className="bg-sub py-[10px] flex items-center md:justify-center gap-x-3"
                socialMediaLinks={campaign.socialMediaLinks}
              />
              {!!campaign?.promotionalMessage && (
                <p className="text-font">{campaign.promotionalMessage}</p>
              )}
              <CampaignDetailsCard
                className="md:hidden grid"
                campaign={campaign}
              />
              <CampaignVtuberDetails
                className="md:hidden block"
                campaign={campaign}
              />
              <CampaignOverviewTabs campaign={campaign} />
            </section>
            <section className="col-span-5 md:block hidden">
              <div className="max-w-[352px] ml-auto grid gap-y-10">
                <CampaignDetailsCard campaign={campaign} />
                <CampaignVtuberDetails campaign={campaign} />
                <CampaignVariantsList variants={campaign?.variants} />
              </div>
            </section>
          </div>
          {relatedCampaigns && relatedCampaigns?.length > 0 && (
            <section className="grid gap-y-10">
              <h3 className="sm:text-4xl text-[22px] font-bold text-font">
                {getText("popular_campaign")}
              </h3>
              <Carousel className="md:flex hidden">
                <CarouselContent className="gap-x-2">
                  {relatedCampaigns?.map((c) => (
                    <CarouselItem
                      key={c.id}
                      className="basis-[28%]">
                      <CampaignCard campaign={c} />
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
              <div className="md:hidden grid grid-cols-2 gap-y-9 gap-x-6">
                {relatedCampaigns?.map((c) => (
                  <CampaignCard
                    key={c.id}
                    campaign={c}
                  />
                ))}
              </div>
            </section>
          )}
        </div>
      </Container>
      <CallToAction />
    </div>
  );
}
