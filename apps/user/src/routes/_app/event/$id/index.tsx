import { Await, createFileRoute, notFound } from "@tanstack/react-router";
import { eventClient, eventParticipantClient } from "@vtuber/services/client";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Container } from "@vtuber/ui/components/container";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { HeaderBreadCrumb } from "@vtuber/ui/components/header-bread-crumb";
import { Image } from "@vtuber/ui/components/image";
import { RouteFallback } from "@vtuber/ui/components/route-fallback";
import { z } from "zod";
import { EnjoyEvent } from "~/components/events/enjoy-event";
import { EventBenefits } from "~/components/events/event-benefits";
import { EventDetailsHeader } from "~/components/events/event-details-header";
import { EventOverview } from "~/components/events/event-overview";
import { EventParticipatingVtubers } from "~/components/events/event-participating-vtubers";
import { EventParticipationFlow } from "~/components/events/event-participation-flow";
import { EventParticipationRequirements } from "~/components/events/event-participation-requirements";
import { EventResultsTab } from "~/components/events/event-results-tabs";
import { CallToAction } from "~/components/layout/call-to-action";
import { EventParticipatingVtubersSkeleton } from "~/components/skeletons/event-participating-vtubers-skeleton";

export const Route = createFileRoute("/_app/event/$id/")({
  component: RouteComponent,
  validateSearch: z.object({
    tab: z.string().optional(),
    commentId: z.number().optional(),
  }),
  loader: async ({ params }) => {
    const [event, eventErr] = await eventClient.getEventById({
      id: params.id,
    });

    if (eventErr) {
      throw notFound({
        data: eventErr?.rawMessage,
      });
    }

    const eventParticipantsPromise =
      eventParticipantClient.getEventParticipantsByEventId({
        eventId: params.id,
      });
    const topParticipantsPromise =
      eventParticipantClient.getTopTenEventParticipantsVtubers({
        eventId: event?.data?.id!,
      });

    return {
      event: event?.data,
      eventParticipantsPromise,
      topParticipantsPromise,
    };
  },
  staleTime: 5000,
});

function RouteComponent() {
  const { event, eventParticipantsPromise } = Route.useLoaderData();

  if (!event) {
    return (
      <RouteFallback
        className="pt-20"
        data={{
          data: "sorry, we couldn't find the event you're looking for.",
        }}
      />
    );
  }
  return (
    <div className="md:pt-20 pt-[92px]">
      <Container className="md:pb-20">
        <HeaderBreadCrumb className="md:hidden flex" />
        <div className="grid grid-cols-12 items-start md:py-0 py-10">
          <section className="md:col-span-7 col-span-12 flex flex-col gap-y-10">
            <EventDetailsHeader event={event} />
            <AspectRatio ratio={768 / 431}>
              <Image
                src={event?.image}
                alt={event?.title}
                className="w-full h-full object-cover"
              />
            </AspectRatio>
            <EventResultsTab event={event} />
            <section className="grid gap-y-8">
              <EventOverview event={event} />
              <EventBenefits event={event} />
            </section>
            <ExternalLinks
              className="bg-sub py-[10px] flex items-center md:justify-center justify-center gap-x-3"
              socialMediaLinks={event.socialMediaLinks}
            />
            <EventParticipationFlow event={event} />
            <EnjoyEvent event={event} />
            <EventParticipationRequirements event={event} />
          </section>
          <section className="col-span-5 md:block hidden sticky top-24 z-20">
            <Await
              promise={eventParticipantsPromise}
              fallback={<EventParticipatingVtubersSkeleton />}
              children={([participants]) => (
                <EventParticipatingVtubers
                  eventParticipants={participants?.data}
                />
              )}
            />
          </section>
        </div>
      </Container>
      <CallToAction />
    </div>
  );
}
