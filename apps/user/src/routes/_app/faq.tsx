import { createFileRoute, notFound, useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { faqClient } from "@vtuber/services/client";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@vtuber/ui/components/accordion";
import { Button } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { AnswerIcon } from "@vtuber/ui/components/icons/answer-icon";
import { QuestionIcon } from "@vtuber/ui/components/icons/question-icon";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { useGetFaqTags } from "@vtuber/ui/hooks/get-faq-tags";
import { Minus, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { z } from "zod";
import { CallToAction } from "~/components/layout/call-to-action";
import { PageTitle } from "~/components/layout/page-title";
import { ENGLISH_KEY, JAPANESE_KEY } from "~/data/constants";

export const Route = createFileRoute("/_app/faq")({
  component: RouteComponent,
  validateSearch: z.object({
    question: z.number().optional(),
  }),
  loader: async () => {
    const language = getCookie("language") || "en";
    const [faqs, err] = await faqClient.getAllActiveFaqs({
      language: language === "en" ? ENGLISH_KEY : JAPANESE_KEY,
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    return faqs?.data;
  },
});

function RouteComponent() {
  const { question } = Route.useSearch();
  const tags = useGetFaqTags();
  const { language, getText } = useLanguage();
  const faqs = Route.useLoaderData();
  const [selectedTag, setSelectedTag] = useState("all");
  const [filteredFaqs, setFilteredFaqs] = useState(faqs);
  const router = useRouter();

  useEffect(() => {
    router.invalidate();
    setFilteredFaqs(faqs);
    setSelectedTag("all");
  }, [language]);

  return (
    <div className="relative pt-20">
      <Container className="pb-32 space-y-16">
        <PageTitle title="faq" />
        <div className="sm:space-y-16 space-y-12 max-w-[960px] mx-auto">
          <div className="flex items-center flex-wrap justify-center sm:gap-6 gap-4">
            {[{ label: getText("all"), value: "all" }, ...tags].map((t) => (
              <Button
                key={t.value}
                onClick={() => {
                  setSelectedTag(t.value);
                  if (t.value === "all") {
                    setFilteredFaqs(faqs);
                  } else {
                    setFilteredFaqs(faqs.filter((f) => f.tag === t.value));
                  }
                }}
                variant={selectedTag === t.value ? "default" : "outline"}
                className=" rounded-full md:min-w-[192px] h-9 hover:bg-white/20 hover:text-tertiary font-medium">
                {t.label === "all" ? getText("all") : t.label}
              </Button>
            ))}
          </div>
          {filteredFaqs && filteredFaqs.length > 0 ? (
            <section className="sm:space-y-16 space-y-8">
              <div
                className={
                  "bg-gradient-5 p-3.5 w-full border-l-8 border-l-[#9376CD] font-bold text-2xl leading-[140%]"
                }>
                {selectedTag === "all" ? getText("all") : selectedTag}
              </div>
              <Accordion
                defaultValue={filteredFaqs[0].id.toString()}
                type="single"
                className="space-y-[24px]"
                collapsible>
                {filteredFaqs?.map((f) => (
                  <AccordionItem
                    key={f.id}
                    value={f.id.toString()}
                    className="border-b-0">
                    <AccordionTrigger className="bg-[#2C2A37] sm:px-10 px-[11px] sm:py-8 py-4 rounded-10 [&>svg]:hidden group">
                      <div className="flex items-center gap-x-6">
                        <div className="sm:size-10 size-7 rounded-full bg-sub items-center justify-center flex">
                          <QuestionIcon className="sm:w-[19px] sm:h-[21px] w-[13.5px] h-[14.5px]" />
                        </div>
                        <h3 className="sm:font-bold font-medium sm:text-xl text-base">
                          {f.question}
                        </h3>
                      </div>
                      <Plus className="!block sm:!size-8 !size-6 group-data-[state=open]:!hidden" />
                      <Minus className="!hidden sm:!size-10 !size-[30px] group-data-[state=open]:!block" />
                    </AccordionTrigger>
                    <AccordionContent className="sm:px-10 px-[11px] sm:py-10 py-8">
                      <div className="flex items-start gap-x-6">
                        <div className="size-7 sm:size-10 rounded-full items-center justify-center flex bg-sub min-w-[1.75rem] sm:min-w-[2.5rem]">
                          <AnswerIcon className="w-[13px] h-[14px] sm:w-[19px] sm:h-[21px]" />
                        </div>
                        <MarkDown
                          markdown={f.response}
                          className="text-font sm:flex-1 flex-auto"
                        />
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </section>
          ) : (
            <div className="h-40 flex items-center justify-center text-3xl font-semibold text-destructive">
              No Content
            </div>
          )}
        </div>
      </Container>
      <CallToAction />
    </div>
  );
}
