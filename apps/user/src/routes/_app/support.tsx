import { Await, createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { staticClient } from "@vtuber/services/client";
import { Card } from "@vtuber/ui/components/card";
import { Container } from "@vtuber/ui/components/container";
import { GradientShape } from "@vtuber/ui/components/shape/gradient";
import { OutlineRectangle } from "@vtuber/ui/components/shape/outline-rectangle";
import { Rectangle } from "@vtuber/ui/components/shape/rectangle";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import { CAN_USE_DOM } from "@vtuber/ui/lib/utils";
import { toast } from "sonner";
import { PageTitle } from "~/components/layout/page-title";

export const Route = createFileRoute("/_app/support")({
  component: RouteComponent,
  loader: async () => {
    const userGuidePromise = staticClient.getAllStaticResource({
      key: "support/user",
    });
    const vtuberGuidePromise = staticClient.getAllStaticResource({
      key: "support/vtuber",
    });

    return {
      vtuberGuidePromise,
      userGuidePromise,
    };
  },
});

function RouteComponent() {
  const { vtuberGuidePromise, userGuidePromise } = Route.useLoaderData();
  const { getText, language } = useLanguage();
  return (
    <div className="relative pt-20">
      <Container className="grid gap-y-16">
        <PageTitle title="support" />
        <section className="space-y-14">
          <p className="text-center text-font font-semibold text-lg">
            {language === "ja"
              ? " V祭では、Vtuberとして参加する方も、ファンとして応援する方も、"
              : "V Matsuri offers features that can be enjoyed"}
            <br />
            {language === "ja"
              ? " それぞれの立場で楽しめる機能をご用意しています。"
              : "by both Vtubers and fans."}
            <br />
            {language === "ja"
              ? "ご利用方法に不安がある方は、まずはこちらのガイドをご覧ください。"
              : "If you're unsure about how to use it, please check out this guide first."}
          </p>

          <div className="grid md:grid-cols-2 items-center justify-center md:gap-x-16 md:gap-y-0 gap-y-10">
            <Await
              promise={userGuidePromise}
              fallback={
                <Skeleton className="w-full min-h-[227px] md:w-[473px] md:ml-auto" />
              }
              children={([res, err]) => (
                <Card
                  onClick={() => {
                    if (res?.data && res?.data.length > 0 && CAN_USE_DOM) {
                      window.open(res?.data[0].value, "_blank");
                      return;
                    }

                    toast.info(getText("coming_soon"));
                  }}
                  className="shadow-none bg-sub rounded-10 border border-blue01 min-h-[227px] flex justify-start items-center pl-6 md:w-[473px] md:ml-auto relative overflow-hidden group">
                  <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 cursor-pointer transition-all ease-in-out duration-300 z-20" />
                  <div className="absolute h-full right-0 top-0 flex flex-col justify-between items-end p-3">
                    <section className="flex items-center gap-x-10">
                      <div className="relative">
                        <GradientShape className="size-5 drop-shadow-light blu[3px]" />
                        <OutlineRectangle className="size-4 blur-[2px] absolute -bottom-2 -right-2" />
                      </div>
                      <Rectangle className="size-3 drop-shadow-light mt-14" />
                      <div className="bg-[#7BC9FF] w-8 h-5 rounded-[2px] drop-shadow-light blur-[4px]" />
                    </section>
                    <GradientShape className="size-[15px] blur-[1px] drop-shadow-light mr-16" />
                    <section className=" flex items-center gap-x-20 pr-6">
                      <GradientShape className="size-[7px] blur-[2px] drop-shadow-light" />
                      <div className="relative">
                        <GradientShape className="size-11 blur-sm" />
                        <Rectangle className="blur-[3px] size-[15px] absolute -top-1 -right-1" />
                      </div>
                    </section>
                  </div>
                  {err ? (
                    <p className="text-center w-full pr-6 text-destructive font-medium">
                      {err?.rawMessage || err?.message}
                    </p>
                  ) : (
                    <div className="font-bold text-[30px] bg-gradient-4 bg-clip-text text-transparent">
                      <p>
                        {language === "ja"
                          ? "お気に入りのVtuberを"
                          : "For those who want to"}
                      </p>
                      <p>
                        {language === "ja"
                          ? "見つけて応援したい方へ"
                          : "Find and support your favorite Vtuber"}
                      </p>
                    </div>
                  )}
                </Card>
              )}
            />

            <Await
              promise={vtuberGuidePromise}
              fallback={
                <Skeleton className="w-full min-h-[227px] md:w-[473px] md:mr-auto" />
              }
              children={([res, err]) => (
                <Card
                  onClick={() => {
                    if (res?.data && res?.data.length > 0 && CAN_USE_DOM) {
                      window.open(res?.data[0].value, "_blank");
                      return;
                    }

                    toast.info(getText("coming_soon"));
                  }}
                  className="shadow-none bg-sub rounded-10 border-purple01 min-h-[227px] flex justify-start items-center pl-6 border relative overflow-hidden md:w-[473px] md:mr-auto group">
                  <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 cursor-pointer transition-all ease-in-out duration-300 z-20" />
                  <div className="absolute h-full right-0 top-0 flex flex-col justify-between items-end">
                    <section className="flex items-center gap-x-36">
                      <GradientShape className="size-2 blur-[2px]" />
                      <div className="relative pr-8 pt-6">
                        <GradientShape className="blur-[10px] size-9" />
                        <OutlineRectangle className="absolute top-3 right-4 blur-sm size-7" />
                      </div>
                    </section>
                    <section className="pr-3 flex items-center">
                      <GradientShape className="size-[15px] drop-shadow-light blur-[1px] mr-6 mt-16" />
                      <div className="bg-[#7BC9FF] w-[19px] h-3 rounded-[2px] drop-shadow-light blur-[2px] mr-20 mb-5" />
                      <Rectangle className="size-[21px] drop-shadow-light mt-5" />
                    </section>
                    <section className="relative pr-24 pb-3">
                      <GradientShape className="size-5 blur-[3px] drop-shadow-light" />
                      <OutlineRectangle className="size-[10px] -translate-y-6 blur-[1px] translate-x-4" />
                    </section>
                  </div>
                  {err ? (
                    <p className="text-center w-full pr-6 text-destructive font-medium">
                      {err?.rawMessage || err?.message}
                    </p>
                  ) : (
                    <div className="font-bold text-[30px] bg-gradient-2 bg-clip-text text-transparent">
                      <p>{getText("for_those_who_want_to")}</p>
                      <p>{getText("participate_in_v_festival")}</p>
                    </div>
                  )}
                </Card>
              )}
            />
          </div>
        </section>
      </Container>
    </div>
  );
}
