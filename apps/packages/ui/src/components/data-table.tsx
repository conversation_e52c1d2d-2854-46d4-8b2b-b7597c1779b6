import { useNavigate, useSearch } from "@tanstack/react-router";
import {
  ColumnDef as TableColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { Button } from "./button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./table";

type DataTableProps<TData> = Omit<
  Parameters<typeof useReactTable<TData>>[0],
  "getCoreRowModel"
> & {
  isPending?: boolean;
  onRowClick?: (row: TData) => void;
  emptyMessage?: string;
  pagination?: {
    currentPage?: number;
    totalPage?: number;
    isLast?: boolean;
    totalItems?: number;
  };
};

export type ColumnDef<Tdata> = TableColumnDef<Tdata>;

export function DataTable<TData>({
  isPending,
  onRowClick,
  pagination,
  emptyMessage,
  ...props
}: DataTableProps<TData>) {
  const navigate = useNavigate();
  // @ts-ignore
  const searchParams = useSearch({ from: "" }) as {
    page: string;
    size: string;
  };

  const table = useReactTable({
    ...props,
    getCoreRowModel: getCoreRowModel(),
  });

  const isDataAvailable = table.getRowModel().rows.length > 0 && !isPending;
  const isError = !isPending && !isDataAvailable;

  const previousPage = () => {
    //  @ts-ignore
    if (searchParams?.page) {
      // @ts-ignore
      const newPage = Number(searchParams.page) - 1;
      navigate({
        search: {
          // @ts-ignore
          page: newPage > 0 ? newPage : undefined,
        },
      });
    }
  };

  const nextPage = () => {
    //  @ts-ignore
    if (searchParams?.page) {
      // @ts-ignore
      const newPage = Number(searchParams.page) + 1;
      navigate({
        // @ts-ignore
        search: (prev) => ({
          ...prev,
          page: newPage,
        }),
      });
    } else {
      navigate({
        // @ts-ignore
        search: (prev) => ({
          ...prev,
          page: 2,
        }),
      });
    }
  };
  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => {
            return (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      onClick={() => header.column.getToggleSortingHandler()}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            );
          })}
        </TableHeader>
        <TableBody>
          <TableRow>
            {isPending && (
              <TableCell
                colSpan={props.columns.length}
                className="text-center h-36">
                Loading...
              </TableCell>
            )}
          </TableRow>
          {isDataAvailable &&
            table.getRowModel().rows.map((row) => (
              <TableRow
                className={onRowClick ? "cursor-pointer" : ""}
                onClick={() => {
                  onRowClick && onRowClick(row.original);
                }}
                key={row.id}
                data-state={row.getIsSelected() && "selected"}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    <p className="text-sm">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      ) || "-"}
                    </p>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          {isError && (
            <TableRow>
              <TableCell
                colSpan={props.columns.length}
                className="h-36 text-center">
                {emptyMessage || "No Results."}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {pagination && (
        <footer className="flex items-center rounded-b-md gap-2 border-t border-t-border justify-end bg-background p-3 py-2">
          <div className="flex items-center">
            <Button
              onClick={() =>
                navigate({
                  // @ts-ignore
                  search: (prev) => ({
                    ...prev,
                    page: 1,
                  }),
                })
              }
              disabled={
                //  @ts-ignore
                !searchParams?.page || searchParams?.page == "1" || isPending
              }
              size={"icon"}
              variant={"ghost"}
              className="rounded-full h-8 w-8">
              <ChevronLeft />
            </Button>
            <Button
              onClick={previousPage}
              size={"icon"}
              disabled={
                //  @ts-ignore
                !searchParams?.page || searchParams?.page == "1" || isPending
              }
              variant={"ghost"}
              className="rounded-full h-8 w-8">
              <ChevronsLeft />
            </Button>
            <Button
              onClick={nextPage}
              disabled={
                pagination.currentPage === pagination.totalPage ||
                isPending ||
                pagination.isLast
              }
              variant={"ghost"}
              size={"icon"}
              className="rounded-full h-8 w-8">
              <ChevronsRight />
            </Button>
            <Button
              onClick={() =>
                navigate({
                  // @ts-ignore
                  search: (prev) => ({
                    ...prev,
                    page: pagination.totalPage,
                  }),
                })
              }
              disabled={pagination.isLast || isPending}
              size={"icon"}
              variant={"ghost"}
              className="rounded-full h-8 w-8">
              <ChevronRight />
            </Button>
          </div>
          <p className="text-sm">
            {/* @ts-ignore */}
            {searchParams?.page || "1"} of {pagination.totalPage || "0"}
          </p>
        </footer>
      )}
    </div>
  );
}
