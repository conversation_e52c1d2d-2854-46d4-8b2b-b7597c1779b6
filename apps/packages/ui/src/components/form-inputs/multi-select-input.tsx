import { Control, FieldValues, Path } from "react-hook-form";
import { InputProps } from "../input";
import { MultipleSelector, MultipleSelectorProps } from "../multi-select";
import { getInputProps, InputField } from "./input-field";

type SelectInputProps<T extends FieldValues> = Omit<
  MultipleSelectorProps,
  "value"
> & {
  control: Control<T>;
  name: Path<T>;
  label: string;
  placeholder?: string;
  description?: string;
  variant?: InputProps["variant"];
  size?: InputProps["size"];
  wrapperClassName?: string;
  className?: string;
  serialize?: (value: string) => unknown;
  stringify?: (value: unknown) => string;
  options: {
    label: string;
    value: string;
    disabled?: boolean;
  }[];
};

export const MultiSelectInput = <T extends FieldValues>(
  props: SelectInputProps<T>,
) => {
  const [fieldProps, inputProps] = getInputProps(props);

  return (
    <InputField
      {...fieldProps}
      wrapperClassName={props.wrapperClassName}
      input={({ field }) => (
        <MultipleSelector
          {...inputProps}
          onChange={(e) => {
            field.onChange(e.map((v) => props.serialize?.(v.value) || v.value));
          }}
          value={field.value}
          options={props.options}
        />
      )}
    />
  );
};
