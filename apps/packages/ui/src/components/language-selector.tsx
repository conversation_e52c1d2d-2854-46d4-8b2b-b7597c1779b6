import { useLanguage } from "@vtuber/language/hooks";
import { cn } from "../lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./dropdown-menu";
import { JapaneseFlag } from "./icons/japanese-flag-icon";
import { USFlag } from "./icons/us-flag-icon";

interface Props {
  className?: string;
  children?: React.ReactNode;
  align?: "start" | "center" | "end";
}

export const LanguageSelector = ({
  children,
  className,
  align = "center",
}: Props) => {
  const { setLanguage, language } = useLanguage();
  const flags: Record<"en" | "ja", React.ReactNode> = {
    en: <USFlag className="size-8" />,
    ja: <JapaneseFlag className="size-8" />,
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className={cn("p-0", className)}>
        {children || flags[language as "en" | "ja"] || (
          <JapaneseFlag className="size-8" />
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align}>
        <DropdownMenuItem onClick={() => setLanguage("en")}>
          <USFlag className="size-6" />
          <p>English</p>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setLanguage("ja")}>
          <JapaneseFlag className="size-6" />
          <p>日本語</p>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
