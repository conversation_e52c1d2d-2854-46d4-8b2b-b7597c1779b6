import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";
import {
  backInUp,
  blurIn,
  blurOut,
  fadeInLeft,
  fadeInRight,
  fadeInUp,
  fadeOutLeft,
  fadeOutRight,
  slideInDown,
  slideInLeft,
  slideInRight,
  slideInUp,
  tada,
  zoomIn,
  zoomInDown,
  zoomOut,
} from "./src/lib/animation";

const config = {
  darkMode: ["class"],
  content: [
    "src/**/*.{ts,tsx}",
    "components/**/*.{ts,tsx}",
    "hooks/**/*.{ts,tsx}",
    "lib/**/*.{ts,tsx}",
    "../packages/ui/src/components/**/*.{ts,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      screens: {
        "2xl": "1536px",
        xl: "1280px",
        lg: "1024px",
        md: "992px",
        sm: "768px",
        xs: "576px",
      },
      fontFamily: {
        noto: ["Noto Sans JP", "sans-serif"],
        montserrat: ["Montserrat", "sans-serif"],
        opensans: ["Open Sans", "sans-serif"],
        mplus: ["M PLUS 1p", "sans-serif"],
      },
      dropShadow: {
        light: "0px 0px 15px #D8FFFF",
        gray: "0px 0px 5px #595959",
      },
      backgroundImage: {
        "gradient-1":
          "linear-gradient(to right, rgba(126, 255, 254, 0.5) 10%, rgba(126, 255, 254, 1) 30%, rgba(135, 250, 206, 1) 40%, rgba(197, 119, 241, 1) 66%, rgba(126, 255, 254, 0.5) 73%)",
        "gradient-2": "linear-gradient(90deg, #9376CD 0%, #85F3FB 100%)",
        "gradient-3": "linear-gradient(90deg, #322A4D 13%, #2F4355 100%)",
        "gradient-4": "linear-gradient(to right, #85F3FB, #9376CD)",
        "gradient-5": "linear-gradient(to right, #56477D,#4C8B95)",
        "gradient-6": "linear-gradient(to bottom, #2F4355 13%, #322A4D 100%)",
        "gradient-gold":
          "linear-gradient(to right, rgba(155, 136, 37, 1) 0%, rgba(181, 154, 22, 1) 12%, rgba(208, 189, 77, 1) 25%, rgba(233, 216, 112, 1) 47%, rgba(238, 227, 159, 1) 57%, rgba(229, 214, 106, 1) 64%, rgba(210, 195, 96, 1) 72%, rgba(200, 169, 0, 1) 82%, rgba(136, 124, 43, 1) 100%)",
        "gradient-silver":
          "linear-gradient(to right, rgba(125, 128, 136, 1) 0%, rgba(172, 160, 176, 1) 10%, rgba(196, 195, 193, 1) 24%, rgba(210, 208, 205, 1) 41%, rgba(214, 211, 208, 1) 44%, rgba(229, 228, 225, 1) 51%, rgba(240, 240, 237, 1) 56%, rgba(235, 236, 233, 1) 61%, rgba(222, 224, 223, 1) 67%, rgba(201, 206, 206, 1) 72%, rgba(177, 174, 179, 1) 88%, rgba(154, 74, 74, 1) 100%)",
        "gradient-blond":
          "linear-gradient(to right, rgba(149, 70, 42, 1) 0%, rgba(178, 97, 33, 1) 12%, rgba(194, 116, 66, 1) 25%, rgba(228, 179, 101, 1) 47%, rgba(235, 208, 170, 1) 57%, rgba(224, 176, 96, 1) 63%, rgba(207, 167, 90, 1) 72%, rgba(197, 134, 18, 1) 82%, rgba(138, 70, 44, 1) 100%)",
        "gradient-gold-linear":
          "linear-gradient(to right, rgba(229, 180, 51, 1) 0%, rgba(242, 182, 24, 1) 30%, rgba(254, 225, 147, 1) 50%, rgba(242, 182, 24, 1) 70%, rgba(229, 180, 51, 1) 100%)",
        "gradient-silver-linear":
          "linear-gradient(to right, #BFBCBC 0%, #B7B7B7 30%, #E2E2E2 50%, #B7B7B7 70%, #BFBCBC 100%)",
        "gradient-blond-linear":
          "linear-gradient(to right, #C47836 0%, #9A6537 30%, #C59469 50%, #9A6537 70%, #C47836 100%)",
      },
      fontSize: {
        pc: ["16px", "200px"],
        "h2-pc": ["48px", "160px"],
        "h2-sub-title-pc": ["20px", "170px"],
        "h2-sp": ["32px", "150px"],
        "h2-sub-title-sp": ["14px", "170px"],
        "p-bold": ["14px", "180px"],
        "p-sp-bold": ["16px", "200px"],
        "p-pc-medium": ["16px", "200px"],
        "p-medium": ["14px", "180px"],
        "p-regular": ["14px", "180px"],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        dark: "hsl(var(--dark))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        golden: "hsl(var(--golden))",
        red01: "hsl(var(--red01))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        blue01: "hsl(var(--blue01))",
        tertiary: "hsl(var(--tertiary))",
        purple01: "hsl(var(--purple01))",
        gray01: "hsl(var(--gray01))",
        green01: "hsl(var(--green01))",
        sub: "hsl(var(--sub))",
        font: "hsl(var(--font))",
        gradient: {
          1: "var(--gradient-1)",
          2: "var(--gradient-2)",
          3: "var(--gradient-3)",
          4: "var(--gradient-4)",
          5: "var(--gradient-5)",
          6: "var(--gradient-6)",
          gold: "var(--gradient-gold)",
          silver: "var(--gradient-silver)",
          blond: "var(--gradient-blond)",
          "gold-linear": "var(--gradient-gold-linear)",
          "silver-linear": "var(--gradient-silver-linear)",
          "blond-linear": "var(--gradient-blond-linear)",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "16px",
        xs: "4px",
        10: "10px",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "fade-in": {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        "blur-out": blurOut,
        "blur-in": blurIn,
        "zoom-in": zoomIn,
        "zoom-out": zoomOut,
        "slide-in-up": slideInUp,
        "slide-in-right": slideInRight,
        "slide-in-left": slideInLeft,
        "slide-in-down": slideInDown,
        tada,
        "back-in-up": backInUp,
        "fade-in-up": fadeInUp,
        "fade-in-right": fadeInRight,
        "fade-out-right": fadeOutRight,
        "fade-in-left": fadeInLeft,
        "fade-out-left": fadeOutLeft,
        "zoom-in-down": zoomInDown,
        float: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-10px)" },
        },
        "pulse-soft": {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.8" },
        },
        shimmer: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.6s ease-out",
        "blur-out": "blur-out 0.8s ease-out",
        "blur-in": "blur-in 0.8s ease-out",
        "zoom-in": "zoom-in 200ms ease-out",
        "zoom-out": "zoom-out 200ms ease-in",
        "slide-in-up": "slide-in-up 0.3s ease-out",
        tada: "tada 2s ease-in-out ",
        "back-in-up": "back-in-up 0.75s ease-in-out",
        "fade-in-up": "fade-in-up 0.2s ease-out",
        "fade-in-right": "fade-in-right 0.2s ease-out",
        "fade-out-right": "fade-out-right 0.2s ease-out",
        "fade-in-left": "fade-in-left 0.2s ease-out",
        "fade-out-left": "fade-out-left 0.2s ease-out",
        "zoom-in-down": "zoom-in-down 0.3s ease-out",
        "slide-in-right": "slide-in-right 0.3s ease-out",
        "slide-in-left": "slide-in-left 0.3s ease-out",
        "slide-in-down": "slide-in-down 0.3s ease-out",
        float: "float 6s ease-in-out infinite",
        "pulse-soft": "pulse-soft 4s ease-in-out infinite",
        shimmer: "shimmer 2.5s ease-in-out infinite",
      },
    },
  },
  plugins: [tailwindcssAnimate],
  safelist: [
    "text-left",
    "text-right",
    "scroll-m-20",
    "text-4xl",
    "text-primary",
    "my-6",
    "font-extrabold",
    "tracking-tight",
    "lg:text-5xl",
    "border-b",
    "min-h-96",
    "text-3xl",
    "font-semibold",
    "first:mt-0",
    "text-2xl",
    "text-xl",
    "text-lg",
    "text-base",
    "leading-7",
    "[&:not(:first-child)]:mt-6",
    "mt-6",
    "border-l-2",
    "pl-6",
    "italic",
    "text-blue-600",
    "hover:underline",
    "hover:cursor-pointer",
    "relative",
    "mx-8",
    "list-disc",
    "mx-2",
    "px-6",
    "outline-none",
    "line-through",
    'before:content-[""]',
    "before:w-4",
    "before:h-4",
    "before:top-0.5",
    "before:left-0",
    "before:cursor-pointer",
    "before:block",
    "before:bg-cover",
    "markdown",
    "before:absolute",
    "before:border",
    "before:border-primary",
    "before:rounded",
    "before:bg-primary",
    "before:bg-no-repeat",
    'after:content-[""]',
    "after:cursor-pointer",
    "after:border-white",
    "after:border-solid",
    "after:absolute",
    "after:block",
    "after:top-[6px]",
    "after:w-[3px]",
    "after:left-[7px]",
    "after:right-[7px]",
    "after:h-[6px]",
    "after:rotate-45",
    "after:border-r-2",
    "after:border-b-2",
    "after:border-l-0",
    "after:border-t-0",
    "before:hidden",
    "after:hidden",
    "my-6",
    "ml-6",
    "list-decimal",
    "[&>li]:mt-2",
    "list-outside",
    "!list-decimal",
    "!list-[upper-roman]",
    "!list-[lower-roman]",
    "!list-[upper-alpha]",
    "!list-[lower-alpha]",
    "m-0",
    "p-0",
    "list-outside",
    "text-blue-600",
    "bg-blue-100",
    "rounded-md",
    "px-1",
    "font-bold",
    "bg-gray-100",
    "p-1",
    "rounded-md",
    "underline",
    "line-through",
    "sub",
    "sup",
    "relative",
    "inline-block",
    "user-select-none",
    "cursor-default",
    "!bg-destructive/50",
    "w-fit",
    "overflow-scroll",
    "border-collapse",
    "w-24",
    "border",
    "px-4",
    "py-2",
    "text-left",
    "[&[align=center]]:text-center",
    "[&[align=right]]:text-right",
    "bg-background",
    "block",
    "border-0",
    "rounded-2xl",
    "w-5",
    "h-5",
    "text-foreground",
    "cursor-pointer",
    "right-1",
    "top-1.5",
    "absolute",
    "z-10",
    "w-5",
    "h-5",
    "rounded-sm",
    "shadow-sm",
    "bg-muted",
    "font-bold",
    "border",
    "border-primary",
    "border-solid",
    "block",
    "h-[calc(100%-2px)]",
    "w-[calc(100%-2px)]",
    "-left-[1px]",
    "-top-[1px]",
    "absolute",
    "-right-1",
    "h-full",
    "w-2",
    "cursor-ew-resize",
    "z-10",
    "top-0",
    "bg-muted",
    "opacity-50",
    "bottom-0",
    "left-0",
    "w-full",
    "h-1",
    "bg-muted",
    "w-[1px]",
    "h-full",
    "bg-primary",
    "top-0",
    "m-0",
    "border-t",
    "p-0",
    "even:bg-muted",
    "ring-2",
    "ring-primary",
    "ring-offset-2",
    "bg-transparent",
    "border",
    "border-dashed",
    "px-4",
    "py-2",
    "grid",
    "gap-2.5",
    "my-2.5",
    "mx-0",
    "text-muted-foreground",
    "user-select-none",
    "ring-2",
    "ring-primary",
    "ring-offset-2",
    "p-0.5",
    "border-none",
    "my-1",
    "mx-0",
    "cursor-pointer",
    'after:content-[""]',
    "after:block",
    "after:h-0.5",
    "after:bg-muted",
    "selected:ring-2",
    "selected:ring-primary",
    "selected:ring-offset-2",
    "selected:user-select-none",
    "[--lexical-indent-base-value:40px]",
    "animate-pulse",
    "bg-gray-400/70",
    "bg-gradient-6",
  ],
} satisfies Config;

export default config;
