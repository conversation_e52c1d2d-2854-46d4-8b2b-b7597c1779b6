// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file content/v1/posts.proto (package api.content.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type { PaginationDetails, PaginationRequest } from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file content/v1/posts.proto.
 */
export const file_content_v1_posts: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.content.v1.AddPostRequest
 */
export type AddPostRequest = Message<"api.content.v1.AddPostRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: optional string media = 3;
   */
  media?: string;

  /**
   * @gotag: validate:"omitempty,oneof=picture video"
   *
   * @generated from field: optional string media_type = 4;
   */
  mediaType?: string;

  /**
   * @generated from field: string name = 5;
   */
  name: string;

  /**
   * @generated from field: bool membership_only = 6;
   */
  membershipOnly: boolean;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 category_id = 7;
   */
  categoryId: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string short_description = 8;
   */
  shortDescription: string;
};

/**
 * Describes the message api.content.v1.AddPostRequest.
 * Use `create(AddPostRequestSchema)` to create a new message.
 */
export const AddPostRequestSchema: GenMessage<AddPostRequest> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 0);

/**
 * @generated from message api.content.v1.AddPostResponse
 */
export type AddPostResponse = Message<"api.content.v1.AddPostResponse"> & {
  /**
   * @generated from field: api.content.v1.Post data = 1;
   */
  data?: Post;
};

/**
 * Describes the message api.content.v1.AddPostResponse.
 * Use `create(AddPostResponseSchema)` to create a new message.
 */
export const AddPostResponseSchema: GenMessage<AddPostResponse> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 1);

/**
 * @generated from message api.content.v1.Post
 */
export type Post = Message<"api.content.v1.Post"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: optional string media = 4;
   */
  media?: string;

  /**
   * @generated from field: optional string media_type = 5;
   */
  mediaType?: string;

  /**
   * @generated from field: string name = 6;
   */
  name: string;

  /**
   * @generated from field: bool membership_only = 7;
   */
  membershipOnly: boolean;

  /**
   * @generated from field: int64 category_id = 8;
   */
  categoryId: bigint;

  /**
   * @generated from field: api.shared.v1.Profile vtuber = 9;
   */
  vtuber?: Profile;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 10;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string short_description = 11;
   */
  shortDescription: string;

  /**
   * @generated from field: int64 post_likes = 12;
   */
  postLikes: bigint;

  /**
   * @generated from field: bool has_liked = 13;
   */
  hasLiked: boolean;

  /**
   * @generated from field: int64 post_comments = 14;
   */
  postComments: bigint;

  /**
   * @generated from field: string slug = 15;
   */
  slug: string;
};

/**
 * Describes the message api.content.v1.Post.
 * Use `create(PostSchema)` to create a new message.
 */
export const PostSchema: GenMessage<Post> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 2);

/**
 * @generated from message api.content.v1.GetAllPostsRequest
 */
export type GetAllPostsRequest = Message<"api.content.v1.GetAllPostsRequest"> & {
  /**
   * @generated from field: optional string vtuber_id = 1;
   */
  vtuberId?: string;

  /**
   * @generated from field: optional int64 category_id = 2;
   */
  categoryId?: bigint;

  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 3;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.content.v1.GetAllPostsRequest.
 * Use `create(GetAllPostsRequestSchema)` to create a new message.
 */
export const GetAllPostsRequestSchema: GenMessage<GetAllPostsRequest> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 3);

/**
 * @generated from message api.content.v1.GetAllPostsResponse
 */
export type GetAllPostsResponse = Message<"api.content.v1.GetAllPostsResponse"> & {
  /**
   * @generated from field: repeated api.content.v1.Post data = 1;
   */
  data: Post[];

  /**
   * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
   */
  paginationDetails?: PaginationDetails;
};

/**
 * Describes the message api.content.v1.GetAllPostsResponse.
 * Use `create(GetAllPostsResponseSchema)` to create a new message.
 */
export const GetAllPostsResponseSchema: GenMessage<GetAllPostsResponse> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 4);

/**
 * @generated from message api.content.v1.GetPostByIdRequest
 */
export type GetPostByIdRequest = Message<"api.content.v1.GetPostByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message api.content.v1.GetPostByIdRequest.
 * Use `create(GetPostByIdRequestSchema)` to create a new message.
 */
export const GetPostByIdRequestSchema: GenMessage<GetPostByIdRequest> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 5);

/**
 * @generated from message api.content.v1.GetPostByIdResponse
 */
export type GetPostByIdResponse = Message<"api.content.v1.GetPostByIdResponse"> & {
  /**
   * @generated from field: api.content.v1.Post data = 1;
   */
  data?: Post;
};

/**
 * Describes the message api.content.v1.GetPostByIdResponse.
 * Use `create(GetPostByIdResponseSchema)` to create a new message.
 */
export const GetPostByIdResponseSchema: GenMessage<GetPostByIdResponse> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 6);

/**
 * @generated from message api.content.v1.DeletePostByIdRequest
 */
export type DeletePostByIdRequest = Message<"api.content.v1.DeletePostByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.content.v1.DeletePostByIdRequest.
 * Use `create(DeletePostByIdRequestSchema)` to create a new message.
 */
export const DeletePostByIdRequestSchema: GenMessage<DeletePostByIdRequest> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 7);

/**
 * @generated from message api.content.v1.GetMyPostsRequest
 */
export type GetMyPostsRequest = Message<"api.content.v1.GetMyPostsRequest"> & {
  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.content.v1.GetMyPostsRequest.
 * Use `create(GetMyPostsRequestSchema)` to create a new message.
 */
export const GetMyPostsRequestSchema: GenMessage<GetMyPostsRequest> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 8);

/**
 * @generated from message api.content.v1.UpdatePostByIdRequest
 */
export type UpdatePostByIdRequest = Message<"api.content.v1.UpdatePostByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: optional string media = 3;
   */
  media?: string;

  /**
   * @gotag: validate:"omitempty,oneof=picture video"
   *
   * @generated from field: optional string media_type = 4;
   */
  mediaType?: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string name = 5;
   */
  name: string;

  /**
   * @generated from field: bool membership_only = 6;
   */
  membershipOnly: boolean;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 category_id = 7;
   */
  categoryId: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 9;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string short_description = 10;
   */
  shortDescription: string;
};

/**
 * Describes the message api.content.v1.UpdatePostByIdRequest.
 * Use `create(UpdatePostByIdRequestSchema)` to create a new message.
 */
export const UpdatePostByIdRequestSchema: GenMessage<UpdatePostByIdRequest> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 9);

/**
 * @generated from message api.content.v1.GetVtuberGalleryRequest
 */
export type GetVtuberGalleryRequest = Message<"api.content.v1.GetVtuberGalleryRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 vtuber_id = 1;
   */
  vtuberId: bigint;

  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.content.v1.GetVtuberGalleryRequest.
 * Use `create(GetVtuberGalleryRequestSchema)` to create a new message.
 */
export const GetVtuberGalleryRequestSchema: GenMessage<GetVtuberGalleryRequest> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 10);

/**
 * @generated from message api.content.v1.DeletePostByIdResponse
 */
export type DeletePostByIdResponse = Message<"api.content.v1.DeletePostByIdResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.content.v1.DeletePostByIdResponse.
 * Use `create(DeletePostByIdResponseSchema)` to create a new message.
 */
export const DeletePostByIdResponseSchema: GenMessage<DeletePostByIdResponse> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 11);

/**
 * @generated from message api.content.v1.UpdatePostByIdResponse
 */
export type UpdatePostByIdResponse = Message<"api.content.v1.UpdatePostByIdResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.content.v1.UpdatePostByIdResponse.
 * Use `create(UpdatePostByIdResponseSchema)` to create a new message.
 */
export const UpdatePostByIdResponseSchema: GenMessage<UpdatePostByIdResponse> = /*@__PURE__*/
  messageDesc(file_content_v1_posts, 12);

/**
 * @generated from service api.content.v1.PostService
 */
export const PostService: GenService<{
  /**
   * @generated from rpc api.content.v1.PostService.AddPost
   */
  addPost: {
    methodKind: "unary";
    input: typeof AddPostRequestSchema;
    output: typeof AddPostResponseSchema;
  },
  /**
   * @generated from rpc api.content.v1.PostService.GetAllPosts
   */
  getAllPosts: {
    methodKind: "unary";
    input: typeof GetAllPostsRequestSchema;
    output: typeof GetAllPostsResponseSchema;
  },
  /**
   * @generated from rpc api.content.v1.PostService.GetPostById
   */
  getPostById: {
    methodKind: "unary";
    input: typeof GetPostByIdRequestSchema;
    output: typeof GetPostByIdResponseSchema;
  },
  /**
   * @generated from rpc api.content.v1.PostService.DeletePostById
   */
  deletePostById: {
    methodKind: "unary";
    input: typeof DeletePostByIdRequestSchema;
    output: typeof DeletePostByIdResponseSchema;
  },
  /**
   * @generated from rpc api.content.v1.PostService.GetMyPosts
   */
  getMyPosts: {
    methodKind: "unary";
    input: typeof GetMyPostsRequestSchema;
    output: typeof GetAllPostsResponseSchema;
  },
  /**
   * @generated from rpc api.content.v1.PostService.UpdatePostById
   */
  updatePostById: {
    methodKind: "unary";
    input: typeof UpdatePostByIdRequestSchema;
    output: typeof UpdatePostByIdResponseSchema;
  },
  /**
   * @generated from rpc api.content.v1.PostService.GetVtuberGalleries
   */
  getVtuberGalleries: {
    methodKind: "unary";
    input: typeof GetVtuberGalleryRequestSchema;
    output: typeof GetAllPostsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_content_v1_posts, 0);

