// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file cms/v1/static.proto (package api.cms.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file cms/v1/static.proto.
 */
export const file_cms_v1_static: GenFile = /*@__PURE__*/
  fileDesc("ChNjbXMvdjEvc3RhdGljLnByb3RvEgphcGkuY21zLnYxIjAKE1VwZGF0ZVN0YXRpY1JlcXVlc3QSCgoCaWQYASABKAMSDQoFdmFsdWUYAiABKAkiQAoQQWRkU3RhdGljUmVxdWVzdBILCgNrZXkYASABKAkSDQoFdmFsdWUYAiABKAkSEAoIbGFuZ3VhZ2UYAyABKAkiIQoTRGVsZXRlU3RhdGljUmVxdWVzdBIKCgJpZBgBIAEoAyKqAQoOU3RhdGljUmVzcG9uc2USCgoCaWQYASABKAMSCwoDa2V5GAIgASgJEg0KBXZhbHVlGAMgASgJEhAKCGxhbmd1YWdlGAQgASgJEi4KCmNyZWF0ZWRfYXQYBSABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wEi4KCnVwZGF0ZWRfYXQYBiABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wIiIKE0dldEFsbFN0YXRpY1JlcXVlc3QSCwoDa2V5GAEgASgJIkAKFEdldEFsbFN0YXRpY1Jlc3BvbnNlEigKBGRhdGEYASADKAsyGi5hcGkuY21zLnYxLlN0YXRpY1Jlc3BvbnNlIjgKFERlbGV0ZVN0YXRpY1Jlc3BvbnNlEg8KB3N1Y2Nlc3MYASABKAgSDwoHbWVzc2FnZRgCIAEoCTLIAwoNU3RhdGljU2VydmljZRJyChRVcGRhdGVTdGF0aWNSZXNvdXJjZRIfLmFwaS5jbXMudjEuVXBkYXRlU3RhdGljUmVxdWVzdBoaLmFwaS5jbXMudjEuU3RhdGljUmVzcG9uc2UiHYK1GBkIASIVX3VzZXIucm9sZSA9PSAnYWRtaW4nElsKFEdldEFsbFN0YXRpY1Jlc291cmNlEh8uYXBpLmNtcy52MS5HZXRBbGxTdGF0aWNSZXF1ZXN0GiAuYXBpLmNtcy52MS5HZXRBbGxTdGF0aWNSZXNwb25zZSIAEmwKEUFkZFN0YXRpY1Jlc291cmNlEhwuYXBpLmNtcy52MS5BZGRTdGF0aWNSZXF1ZXN0GhouYXBpLmNtcy52MS5TdGF0aWNSZXNwb25zZSIdgrUYGQgBIhVfdXNlci5yb2xlID09ICdhZG1pbicSeAoURGVsZXRlU3RhdGljUmVzb3VyY2USHy5hcGkuY21zLnYxLkRlbGV0ZVN0YXRpY1JlcXVlc3QaIC5hcGkuY21zLnYxLkRlbGV0ZVN0YXRpY1Jlc3BvbnNlIh2CtRgZCAEiFV91c2VyLnJvbGUgPT0gJ2FkbWluJ0IsWipnaXRodWIuY29tL25zcC1pbmMvdnR1YmVyL2FwaS9jbXMvdjE7Y21zdjFiBnByb3RvMw", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.cms.v1.UpdateStaticRequest
 */
export type UpdateStaticRequest = Message<"api.cms.v1.UpdateStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string value = 2;
   */
  value: string;
};

/**
 * Describes the message api.cms.v1.UpdateStaticRequest.
 * Use `create(UpdateStaticRequestSchema)` to create a new message.
 */
export const UpdateStaticRequestSchema: GenMessage<UpdateStaticRequest> = /*@__PURE__*/
  messageDesc(file_cms_v1_static, 0);

/**
 * @generated from message api.cms.v1.AddStaticRequest
 */
export type AddStaticRequest = Message<"api.cms.v1.AddStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string value = 2;
   */
  value: string;

  /**
   * @gotag: validate:"required,oneof=en-us ja-jp"
   *
   * @generated from field: string language = 3;
   */
  language: string;
};

/**
 * Describes the message api.cms.v1.AddStaticRequest.
 * Use `create(AddStaticRequestSchema)` to create a new message.
 */
export const AddStaticRequestSchema: GenMessage<AddStaticRequest> = /*@__PURE__*/
  messageDesc(file_cms_v1_static, 1);

/**
 * @generated from message api.cms.v1.DeleteStaticRequest
 */
export type DeleteStaticRequest = Message<"api.cms.v1.DeleteStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.cms.v1.DeleteStaticRequest.
 * Use `create(DeleteStaticRequestSchema)` to create a new message.
 */
export const DeleteStaticRequestSchema: GenMessage<DeleteStaticRequest> = /*@__PURE__*/
  messageDesc(file_cms_v1_static, 2);

/**
 * @generated from message api.cms.v1.StaticResponse
 */
export type StaticResponse = Message<"api.cms.v1.StaticResponse"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string key = 2;
   */
  key: string;

  /**
   * @generated from field: string value = 3;
   */
  value: string;

  /**
   * @generated from field: string language = 4;
   */
  language: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 6;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message api.cms.v1.StaticResponse.
 * Use `create(StaticResponseSchema)` to create a new message.
 */
export const StaticResponseSchema: GenMessage<StaticResponse> = /*@__PURE__*/
  messageDesc(file_cms_v1_static, 3);

/**
 * @generated from message api.cms.v1.GetAllStaticRequest
 */
export type GetAllStaticRequest = Message<"api.cms.v1.GetAllStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string key = 1;
   */
  key: string;
};

/**
 * Describes the message api.cms.v1.GetAllStaticRequest.
 * Use `create(GetAllStaticRequestSchema)` to create a new message.
 */
export const GetAllStaticRequestSchema: GenMessage<GetAllStaticRequest> = /*@__PURE__*/
  messageDesc(file_cms_v1_static, 4);

/**
 * @generated from message api.cms.v1.GetAllStaticResponse
 */
export type GetAllStaticResponse = Message<"api.cms.v1.GetAllStaticResponse"> & {
  /**
   * @generated from field: repeated api.cms.v1.StaticResponse data = 1;
   */
  data: StaticResponse[];
};

/**
 * Describes the message api.cms.v1.GetAllStaticResponse.
 * Use `create(GetAllStaticResponseSchema)` to create a new message.
 */
export const GetAllStaticResponseSchema: GenMessage<GetAllStaticResponse> = /*@__PURE__*/
  messageDesc(file_cms_v1_static, 5);

/**
 * @generated from message api.cms.v1.DeleteStaticResponse
 */
export type DeleteStaticResponse = Message<"api.cms.v1.DeleteStaticResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.cms.v1.DeleteStaticResponse.
 * Use `create(DeleteStaticResponseSchema)` to create a new message.
 */
export const DeleteStaticResponseSchema: GenMessage<DeleteStaticResponse> = /*@__PURE__*/
  messageDesc(file_cms_v1_static, 6);

/**
 * @generated from service api.cms.v1.StaticService
 */
export const StaticService: GenService<{
  /**
   * @generated from rpc api.cms.v1.StaticService.UpdateStaticResource
   */
  updateStaticResource: {
    methodKind: "unary";
    input: typeof UpdateStaticRequestSchema;
    output: typeof StaticResponseSchema;
  },
  /**
   * @generated from rpc api.cms.v1.StaticService.GetAllStaticResource
   */
  getAllStaticResource: {
    methodKind: "unary";
    input: typeof GetAllStaticRequestSchema;
    output: typeof GetAllStaticResponseSchema;
  },
  /**
   * @generated from rpc api.cms.v1.StaticService.AddStaticResource
   */
  addStaticResource: {
    methodKind: "unary";
    input: typeof AddStaticRequestSchema;
    output: typeof StaticResponseSchema;
  },
  /**
   * @generated from rpc api.cms.v1.StaticService.DeleteStaticResource
   */
  deleteStaticResource: {
    methodKind: "unary";
    input: typeof DeleteStaticRequestSchema;
    output: typeof DeleteStaticResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_cms_v1_static, 0);

