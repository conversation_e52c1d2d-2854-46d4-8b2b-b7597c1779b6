// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file shared/v1/profile.proto (package api.shared.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file shared/v1/profile.proto.
 */
export const file_shared_v1_profile: GenFile = /*@__PURE__*/
  fileDesc("ChdzaGFyZWQvdjEvcHJvZmlsZS5wcm90bxINYXBpLnNoYXJlZC52MSKRAQoHUHJvZmlsZRIKCgJpZBgBIAEoAxIMCgRuYW1lGAIgASgJEhIKBWltYWdlGAMgASgJSACIAQESFQoIZnVyaWdhbmEYBCABKAlIAYgBARIZCgxpbnRyb2R1Y3Rpb24YBSABKAlIAogBAUIICgZfaW1hZ2VCCwoJX2Z1cmlnYW5hQg8KDV9pbnRyb2R1Y3Rpb25CMlowZ2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvc2hhcmVkL3YxO3NoYXJlZHYxYgZwcm90bzM");

/**
 * @generated from message api.shared.v1.Profile
 */
export type Profile = Message<"api.shared.v1.Profile"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: optional string image = 3;
   */
  image?: string;

  /**
   * @generated from field: optional string furigana = 4;
   */
  furigana?: string;

  /**
   * @generated from field: optional string introduction = 5;
   */
  introduction?: string;
};

/**
 * Describes the message api.shared.v1.Profile.
 * Use `create(ProfileSchema)` to create a new message.
 */
export const ProfileSchema: GenMessage<Profile> = /*@__PURE__*/
  messageDesc(file_shared_v1_profile, 0);

