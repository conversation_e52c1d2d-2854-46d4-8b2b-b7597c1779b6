// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file events/v1/events.proto (package api.events.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type { PaginationDetails, PaginationRequest } from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import type { SocialMediaLinks } from "../../shared/v1/social_media_links_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file events/v1/events.proto.
 */
export const file_events_v1_events: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.events.v1.AddEventRequest
 */
export type AddEventRequest = Message<"api.events.v1.AddEventRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string image = 3;
   */
  image: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string rules = 5;
   */
  rules: string;

  /**
   * @generated from field: google.protobuf.Timestamp start_date = 6;
   */
  startDate?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_date = 7;
   */
  endDate?: Timestamp;

  /**
   * @generated from field: optional int64 category_id = 8;
   */
  categoryId?: bigint;

  /**
   * @generated from field: optional bool as_vtuber = 9;
   */
  asVtuber?: boolean;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string short_description = 10;
   */
  shortDescription: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string participation_flow = 11;
   */
  participationFlow: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string benefits = 12;
   */
  benefits: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string requirements = 13;
   */
  requirements: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string overview = 14;
   */
  overview: string;

  /**
   * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 15;
   */
  socialMediaLinks?: SocialMediaLinks;
};

/**
 * Describes the message api.events.v1.AddEventRequest.
 * Use `create(AddEventRequestSchema)` to create a new message.
 */
export const AddEventRequestSchema: GenMessage<AddEventRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 0);

/**
 * @generated from message api.events.v1.AddEventResponse
 */
export type AddEventResponse = Message<"api.events.v1.AddEventResponse"> & {
  /**
   * @generated from field: api.events.v1.Event data = 1;
   */
  data?: Event;
};

/**
 * Describes the message api.events.v1.AddEventResponse.
 * Use `create(AddEventResponseSchema)` to create a new message.
 */
export const AddEventResponseSchema: GenMessage<AddEventResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 1);

/**
 * @generated from message api.events.v1.Event
 */
export type Event = Message<"api.events.v1.Event"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string image = 4;
   */
  image: string;

  /**
   * @generated from field: string rules = 5;
   */
  rules: string;

  /**
   * @generated from field: google.protobuf.Timestamp start_date = 6;
   */
  startDate?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_date = 7;
   */
  endDate?: Timestamp;

  /**
   * @generated from field: optional int64 category_id = 8;
   */
  categoryId?: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 9;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: optional api.shared.v1.Profile user = 10;
   */
  user?: Profile;

  /**
   * @generated from field: optional api.shared.v1.Profile vtuber = 11;
   */
  vtuber?: Profile;

  /**
   * @generated from field: string status = 12;
   */
  status: string;

  /**
   * @generated from field: bool has_participated = 13;
   */
  hasParticipated: boolean;

  /**
   * @generated from field: string short_description = 14;
   */
  shortDescription: string;

  /**
   * @generated from field: string participation_flow = 15;
   */
  participationFlow: string;

  /**
   * @generated from field: string benefits = 16;
   */
  benefits: string;

  /**
   * @generated from field: string requirements = 17;
   */
  requirements: string;

  /**
   * @generated from field: string overview = 18;
   */
  overview: string;

  /**
   * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 19;
   */
  socialMediaLinks?: SocialMediaLinks;

  /**
   * @generated from field: string slug = 20;
   */
  slug: string;
};

/**
 * Describes the message api.events.v1.Event.
 * Use `create(EventSchema)` to create a new message.
 */
export const EventSchema: GenMessage<Event> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 2);

/**
 * @generated from message api.events.v1.GetAllEventsRequest
 */
export type GetAllEventsRequest = Message<"api.events.v1.GetAllEventsRequest"> & {
  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
   */
  pagination?: PaginationRequest;

  /**
   * @generated from field: optional int64 vtuber_id = 2;
   */
  vtuberId?: bigint;
};

/**
 * Describes the message api.events.v1.GetAllEventsRequest.
 * Use `create(GetAllEventsRequestSchema)` to create a new message.
 */
export const GetAllEventsRequestSchema: GenMessage<GetAllEventsRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 3);

/**
 * @generated from message api.events.v1.GetAllEventsResponse
 */
export type GetAllEventsResponse = Message<"api.events.v1.GetAllEventsResponse"> & {
  /**
   * @generated from field: repeated api.events.v1.Event data = 1;
   */
  data: Event[];

  /**
   * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
   */
  paginationDetails?: PaginationDetails;
};

/**
 * Describes the message api.events.v1.GetAllEventsResponse.
 * Use `create(GetAllEventsResponseSchema)` to create a new message.
 */
export const GetAllEventsResponseSchema: GenMessage<GetAllEventsResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 4);

/**
 * @generated from message api.events.v1.GetAllEventsByCategoryRequest
 */
export type GetAllEventsByCategoryRequest = Message<"api.events.v1.GetAllEventsByCategoryRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 category_id = 1;
   */
  categoryId: bigint;

  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.events.v1.GetAllEventsByCategoryRequest.
 * Use `create(GetAllEventsByCategoryRequestSchema)` to create a new message.
 */
export const GetAllEventsByCategoryRequestSchema: GenMessage<GetAllEventsByCategoryRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 5);

/**
 * @generated from message api.events.v1.GetEventByIdRequest
 */
export type GetEventByIdRequest = Message<"api.events.v1.GetEventByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message api.events.v1.GetEventByIdRequest.
 * Use `create(GetEventByIdRequestSchema)` to create a new message.
 */
export const GetEventByIdRequestSchema: GenMessage<GetEventByIdRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 6);

/**
 * @generated from message api.events.v1.GetEventByIdResponse
 */
export type GetEventByIdResponse = Message<"api.events.v1.GetEventByIdResponse"> & {
  /**
   * @generated from field: api.events.v1.Event data = 1;
   */
  data?: Event;
};

/**
 * Describes the message api.events.v1.GetEventByIdResponse.
 * Use `create(GetEventByIdResponseSchema)` to create a new message.
 */
export const GetEventByIdResponseSchema: GenMessage<GetEventByIdResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 7);

/**
 * @generated from message api.events.v1.DeleteEventByIdRequest
 */
export type DeleteEventByIdRequest = Message<"api.events.v1.DeleteEventByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.events.v1.DeleteEventByIdRequest.
 * Use `create(DeleteEventByIdRequestSchema)` to create a new message.
 */
export const DeleteEventByIdRequestSchema: GenMessage<DeleteEventByIdRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 8);

/**
 * @generated from message api.events.v1.UpdateEventByIdRequest
 */
export type UpdateEventByIdRequest = Message<"api.events.v1.UpdateEventByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: string image = 3;
   */
  image: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string rules = 4;
   */
  rules: string;

  /**
   * @generated from field: google.protobuf.Timestamp start_date = 5;
   */
  startDate?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_date = 6;
   */
  endDate?: Timestamp;

  /**
   * @generated from field: optional int64 category_id = 7;
   */
  categoryId?: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 8;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string short_description = 9;
   */
  shortDescription: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string participation_flow = 10;
   */
  participationFlow: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string benefits = 11;
   */
  benefits: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string requirements = 12;
   */
  requirements: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string overview = 13;
   */
  overview: string;

  /**
   * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 14;
   */
  socialMediaLinks?: SocialMediaLinks;
};

/**
 * Describes the message api.events.v1.UpdateEventByIdRequest.
 * Use `create(UpdateEventByIdRequestSchema)` to create a new message.
 */
export const UpdateEventByIdRequestSchema: GenMessage<UpdateEventByIdRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 9);

/**
 * @generated from message api.events.v1.ApproveOrRejectEventRequest
 */
export type ApproveOrRejectEventRequest = Message<"api.events.v1.ApproveOrRejectEventRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"omitempty,oneof=approved rejected"
   *
   * @generated from field: string status = 2;
   */
  status: string;
};

/**
 * Describes the message api.events.v1.ApproveOrRejectEventRequest.
 * Use `create(ApproveOrRejectEventRequestSchema)` to create a new message.
 */
export const ApproveOrRejectEventRequestSchema: GenMessage<ApproveOrRejectEventRequest> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 10);

/**
 * @generated from message api.events.v1.DeleteEventByIdResponse
 */
export type DeleteEventByIdResponse = Message<"api.events.v1.DeleteEventByIdResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.events.v1.DeleteEventByIdResponse.
 * Use `create(DeleteEventByIdResponseSchema)` to create a new message.
 */
export const DeleteEventByIdResponseSchema: GenMessage<DeleteEventByIdResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 11);

/**
 * @generated from message api.events.v1.UpdateEventByIdResponse
 */
export type UpdateEventByIdResponse = Message<"api.events.v1.UpdateEventByIdResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.events.v1.UpdateEventByIdResponse.
 * Use `create(UpdateEventByIdResponseSchema)` to create a new message.
 */
export const UpdateEventByIdResponseSchema: GenMessage<UpdateEventByIdResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 12);

/**
 * @generated from message api.events.v1.ApproveOrRejectEventResponse
 */
export type ApproveOrRejectEventResponse = Message<"api.events.v1.ApproveOrRejectEventResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.events.v1.ApproveOrRejectEventResponse.
 * Use `create(ApproveOrRejectEventResponseSchema)` to create a new message.
 */
export const ApproveOrRejectEventResponseSchema: GenMessage<ApproveOrRejectEventResponse> = /*@__PURE__*/
  messageDesc(file_events_v1_events, 13);

/**
 * @generated from service api.events.v1.EventService
 */
export const EventService: GenService<{
  /**
   * @generated from rpc api.events.v1.EventService.AddEvent
   */
  addEvent: {
    methodKind: "unary";
    input: typeof AddEventRequestSchema;
    output: typeof AddEventResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.GetAllEvents
   */
  getAllEvents: {
    methodKind: "unary";
    input: typeof GetAllEventsRequestSchema;
    output: typeof GetAllEventsResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.GetEventById
   */
  getEventById: {
    methodKind: "unary";
    input: typeof GetEventByIdRequestSchema;
    output: typeof GetEventByIdResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.DeleteEventById
   */
  deleteEventById: {
    methodKind: "unary";
    input: typeof DeleteEventByIdRequestSchema;
    output: typeof DeleteEventByIdResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.UpdateEventById
   */
  updateEventById: {
    methodKind: "unary";
    input: typeof UpdateEventByIdRequestSchema;
    output: typeof UpdateEventByIdResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.GetAllEventsByCategory
   */
  getAllEventsByCategory: {
    methodKind: "unary";
    input: typeof GetAllEventsByCategoryRequestSchema;
    output: typeof GetAllEventsResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.ApproveOrRejectEvent
   */
  approveOrRejectEvent: {
    methodKind: "unary";
    input: typeof ApproveOrRejectEventRequestSchema;
    output: typeof ApproveOrRejectEventResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.GetMyEvents
   */
  getMyEvents: {
    methodKind: "unary";
    input: typeof GetAllEventsRequestSchema;
    output: typeof GetAllEventsResponseSchema;
  },
  /**
   * @generated from rpc api.events.v1.EventService.GetUserEvents
   */
  getUserEvents: {
    methodKind: "unary";
    input: typeof GetAllEventsRequestSchema;
    output: typeof GetAllEventsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_events_v1_events, 0);

