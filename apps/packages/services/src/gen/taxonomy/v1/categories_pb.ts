// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file taxonomy/v1/categories.proto (package api.taxonomy.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file taxonomy/v1/categories.proto.
 */
export const file_taxonomy_v1_categories: GenFile = /*@__PURE__*/
  fileDesc("Chx0YXhvbm9teS92MS9jYXRlZ29yaWVzLnByb3RvEg9hcGkudGF4b25vbXkudjEiRgoSQWRkQ2F0ZWdvcnlSZXF1ZXN0EgwKBG5hbWUYASABKAkSEwoLZGVzY3JpcHRpb24YAiABKAkSDQoFaW1hZ2UYAyABKAkiPgoTQWRkQ2F0ZWdvcnlSZXNwb25zZRInCgRkYXRhGAEgASgLMhkuYXBpLnRheG9ub215LnYxLkNhdGVnb3J5IiAKEkdldENhdGVnb3J5UmVxdWVzdBIKCgJpZBgBIAEoAyI+ChNHZXRDYXRlZ29yeVJlc3BvbnNlEicKBGRhdGEYASABKAsyGS5hcGkudGF4b25vbXkudjEuQ2F0ZWdvcnkiVQoVVXBkYXRlQ2F0ZWdvcnlSZXF1ZXN0EgoKAmlkGAEgASgDEgwKBG5hbWUYAiABKAkSEwoLZGVzY3JpcHRpb24YAyABKAkSDQoFaW1hZ2UYBCABKAkiIwoVRGVsZXRlQ2F0ZWdvcnlSZXF1ZXN0EgoKAmlkGAEgASgDIoYBCghDYXRlZ29yeRIKCgJpZBgBIAEoAxIMCgRuYW1lGAIgASgJEhMKC2Rlc2NyaXB0aW9uGAMgASgJEg0KBWltYWdlGAQgASgJEi4KCmNyZWF0ZWRfYXQYBSABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wEgwKBHNsdWcYBiABKAkiGQoXR2V0QWxsQ2F0ZWdvcmllc1JlcXVlc3QiSQoYR2V0QWxsQ2F0ZWdvcmllc1Jlc3BvbnNlEi0KCmNhdGVnb3JpZXMYASADKAsyGS5hcGkudGF4b25vbXkudjEuQ2F0ZWdvcnkygAQKD0NhdGVnb3J5U2VydmljZRJiCgtBZGRDYXRlZ29yeRIjLmFwaS50YXhvbm9teS52MS5BZGRDYXRlZ29yeVJlcXVlc3QaJC5hcGkudGF4b25vbXkudjEuQWRkQ2F0ZWdvcnlSZXNwb25zZSIIgrUYBAgBEAESZwoQR2V0QWxsQ2F0ZWdvcmllcxIoLmFwaS50YXhvbm9teS52MS5HZXRBbGxDYXRlZ29yaWVzUmVxdWVzdBopLmFwaS50YXhvbm9teS52MS5HZXRBbGxDYXRlZ29yaWVzUmVzcG9uc2USWAoLR2V0Q2F0ZWdvcnkSIy5hcGkudGF4b25vbXkudjEuR2V0Q2F0ZWdvcnlSZXF1ZXN0GiQuYXBpLnRheG9ub215LnYxLkdldENhdGVnb3J5UmVzcG9uc2USYgoOVXBkYXRlQ2F0ZWdvcnkSJi5hcGkudGF4b25vbXkudjEuVXBkYXRlQ2F0ZWdvcnlSZXF1ZXN0Gh4uYXBpLnNoYXJlZC52MS5HZW5lcmljUmVzcG9uc2UiCIK1GAQIARABEmIKDkRlbGV0ZUNhdGVnb3J5EiYuYXBpLnRheG9ub215LnYxLkRlbGV0ZUNhdGVnb3J5UmVxdWVzdBoeLmFwaS5zaGFyZWQudjEuR2VuZXJpY1Jlc3BvbnNlIgiCtRgECAEQAUI2WjRnaXRodWIuY29tL25zcC1pbmMvdnR1YmVyL2FwaS90YXhvbm9teS92MTt0YXhvbm9teXYxYgZwcm90bzM", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination, file_shared_v1_profile, file_shared_v1_social_media_links]);

/**
 * @generated from message api.taxonomy.v1.AddCategoryRequest
 */
export type AddCategoryRequest = Message<"api.taxonomy.v1.AddCategoryRequest"> & {
  /**
   * @gotag: validate:"required,min=1"
   *
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @gotag: validate:"required,min=1"
   *
   * @generated from field: string image = 3;
   */
  image: string;
};

/**
 * Describes the message api.taxonomy.v1.AddCategoryRequest.
 * Use `create(AddCategoryRequestSchema)` to create a new message.
 */
export const AddCategoryRequestSchema: GenMessage<AddCategoryRequest> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 0);

/**
 * @generated from message api.taxonomy.v1.AddCategoryResponse
 */
export type AddCategoryResponse = Message<"api.taxonomy.v1.AddCategoryResponse"> & {
  /**
   * @generated from field: api.taxonomy.v1.Category data = 1;
   */
  data?: Category;
};

/**
 * Describes the message api.taxonomy.v1.AddCategoryResponse.
 * Use `create(AddCategoryResponseSchema)` to create a new message.
 */
export const AddCategoryResponseSchema: GenMessage<AddCategoryResponse> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 1);

/**
 * @generated from message api.taxonomy.v1.GetCategoryRequest
 */
export type GetCategoryRequest = Message<"api.taxonomy.v1.GetCategoryRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.taxonomy.v1.GetCategoryRequest.
 * Use `create(GetCategoryRequestSchema)` to create a new message.
 */
export const GetCategoryRequestSchema: GenMessage<GetCategoryRequest> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 2);

/**
 * @generated from message api.taxonomy.v1.GetCategoryResponse
 */
export type GetCategoryResponse = Message<"api.taxonomy.v1.GetCategoryResponse"> & {
  /**
   * @generated from field: api.taxonomy.v1.Category data = 1;
   */
  data?: Category;
};

/**
 * Describes the message api.taxonomy.v1.GetCategoryResponse.
 * Use `create(GetCategoryResponseSchema)` to create a new message.
 */
export const GetCategoryResponseSchema: GenMessage<GetCategoryResponse> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 3);

/**
 * @generated from message api.taxonomy.v1.UpdateCategoryRequest
 */
export type UpdateCategoryRequest = Message<"api.taxonomy.v1.UpdateCategoryRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string image = 4;
   */
  image: string;
};

/**
 * Describes the message api.taxonomy.v1.UpdateCategoryRequest.
 * Use `create(UpdateCategoryRequestSchema)` to create a new message.
 */
export const UpdateCategoryRequestSchema: GenMessage<UpdateCategoryRequest> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 4);

/**
 * @generated from message api.taxonomy.v1.DeleteCategoryRequest
 */
export type DeleteCategoryRequest = Message<"api.taxonomy.v1.DeleteCategoryRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.taxonomy.v1.DeleteCategoryRequest.
 * Use `create(DeleteCategoryRequestSchema)` to create a new message.
 */
export const DeleteCategoryRequestSchema: GenMessage<DeleteCategoryRequest> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 5);

/**
 * @generated from message api.taxonomy.v1.Category
 */
export type Category = Message<"api.taxonomy.v1.Category"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string image = 4;
   */
  image: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string slug = 6;
   */
  slug: string;
};

/**
 * Describes the message api.taxonomy.v1.Category.
 * Use `create(CategorySchema)` to create a new message.
 */
export const CategorySchema: GenMessage<Category> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 6);

/**
 * @generated from message api.taxonomy.v1.GetAllCategoriesRequest
 */
export type GetAllCategoriesRequest = Message<"api.taxonomy.v1.GetAllCategoriesRequest"> & {
};

/**
 * Describes the message api.taxonomy.v1.GetAllCategoriesRequest.
 * Use `create(GetAllCategoriesRequestSchema)` to create a new message.
 */
export const GetAllCategoriesRequestSchema: GenMessage<GetAllCategoriesRequest> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 7);

/**
 * @generated from message api.taxonomy.v1.GetAllCategoriesResponse
 */
export type GetAllCategoriesResponse = Message<"api.taxonomy.v1.GetAllCategoriesResponse"> & {
  /**
   * @generated from field: repeated api.taxonomy.v1.Category categories = 1;
   */
  categories: Category[];
};

/**
 * Describes the message api.taxonomy.v1.GetAllCategoriesResponse.
 * Use `create(GetAllCategoriesResponseSchema)` to create a new message.
 */
export const GetAllCategoriesResponseSchema: GenMessage<GetAllCategoriesResponse> = /*@__PURE__*/
  messageDesc(file_taxonomy_v1_categories, 8);

/**
 * @generated from service api.taxonomy.v1.CategoryService
 */
export const CategoryService: GenService<{
  /**
   * @generated from rpc api.taxonomy.v1.CategoryService.AddCategory
   */
  addCategory: {
    methodKind: "unary";
    input: typeof AddCategoryRequestSchema;
    output: typeof AddCategoryResponseSchema;
  },
  /**
   * @generated from rpc api.taxonomy.v1.CategoryService.GetAllCategories
   */
  getAllCategories: {
    methodKind: "unary";
    input: typeof GetAllCategoriesRequestSchema;
    output: typeof GetAllCategoriesResponseSchema;
  },
  /**
   * @generated from rpc api.taxonomy.v1.CategoryService.GetCategory
   */
  getCategory: {
    methodKind: "unary";
    input: typeof GetCategoryRequestSchema;
    output: typeof GetCategoryResponseSchema;
  },
  /**
   * @generated from rpc api.taxonomy.v1.CategoryService.UpdateCategory
   */
  updateCategory: {
    methodKind: "unary";
    input: typeof UpdateCategoryRequestSchema;
    output: typeof GenericResponseSchema;
  },
  /**
   * @generated from rpc api.taxonomy.v1.CategoryService.DeleteCategory
   */
  deleteCategory: {
    methodKind: "unary";
    input: typeof DeleteCategoryRequestSchema;
    output: typeof GenericResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_taxonomy_v1_categories, 0);

