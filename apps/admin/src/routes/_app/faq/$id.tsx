import { createFileRoute, notFound } from "@tanstack/react-router";
import { faqClient } from "@vtuber/services/client";
import { FaqForm } from "~/components/faq-form";

export const Route = createFileRoute("/_app/faq/$id")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [faq, err] = await faqClient.getFaq({
      id: BigInt(params.id),
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    return faq;
  },
});

function RouteComponent() {
  const faq = Route.useLoaderData();

  return <FaqForm faq={faq?.data} />;
}
