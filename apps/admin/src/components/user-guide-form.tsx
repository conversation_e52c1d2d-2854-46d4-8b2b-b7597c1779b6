import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { StaticService } from "@vtuber/services/cms";
import { Button } from "@vtuber/ui/components/button";
import {
  <PERSON>alog,
  DialogClose,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Form } from "@vtuber/ui/components/form";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Plus, Save } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

interface Props {
  type: string;
  content?: string;
  id?: bigint;
}

const schema = z.object({
  key: z.string().min(1),
  value: z
    .string()
    .url()
    .refine(
      (val) => {
        try {
          const url = new URL(val);
          return url.hostname.includes(".");
        } catch (e) {
          return false;
        }
      },
      {
        message: "URL must include a domain (e.g., .com, .org)",
      },
    ),
  language: z.string().min(1),
});

export const UserGuideForm = ({ type, content, id }: Props) => {
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const form = useForm({
    defaultValues: {
      key: type,
      value: content,
      language: "en-us",
    },
  });

  const { mutate: updateMutation, isPending: isUpdating } = useMutation(
    StaticService.method.updateStaticResource,
    {
      onSuccess: () => {
        toast.success(type.replace(/[^\w\s]/gi, " ") + " updated successfully");
        setOpen(false);
        router.invalidate();
        form.reset();
      },
      onError: (err) => {
        toast.error(err.rawMessage);
      },
    },
  );
  const { mutate: addMutation, isPending: isAdding } = useMutation(
    StaticService.method.addStaticResource,
    {
      onSuccess: () => {
        toast.success(type?.replace(/[^\w\s]/gi, " ") + " added successfully");
        setOpen(false);
        router.invalidate();
        form.reset();
      },
      onError: (err) => {
        toast.error(err.rawMessage);
      },
    },
  );

  const onSubmit = form.handleSubmit((data) => {
    const isValid = schema.safeParse(data);
    if (!isValid.success) {
      form.setError("value", {
        type: "server",
        message: "Invalid URL",
      });
      return;
    }

    if (!!id) {
      updateMutation({
        id: BigInt(id ?? 0n),
        value: data.value,
      });

      return;
    }

    addMutation({
      key: data.key,
      value: data.value,
      language: data.language,
    });
  });

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={"outline-dark"}
          className="w-full"
          size={"xl"}>
          {!!id ? <Save className="mr-2" /> : <Plus className="mr-2" />}{" "}
          {!!id ? "Update" : "Add"}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {!!id ? "Update" : "Add"} {type.replace(/[^\w\s]/gi, " ")}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form className="space-y-6 pt-6">
            <SelectInput
              disabled
              variant={"muted"}
              size={"lg"}
              label="Type"
              control={form.control}
              name="key"
              options={[
                {
                  label: "Vtuber",
                  value: "support/vtuber",
                },
                {
                  label: "User",
                  value: "support/user",
                },
              ]}
            />
            <TextInput
              variant={"muted"}
              size={"lg"}
              control={form.control}
              name="value"
              label="URL"
              placeholder="https://**********"
              type="url"
              inputMode="url"
            />
          </form>
        </Form>
        <DialogFooter className="gap-x-3 pt-3">
          <DialogClose>Cancel</DialogClose>
          <Button
            loading={isAdding || isUpdating}
            onClick={onSubmit}
            variant={"success"}>
            {!!id ? "Update" : "Add"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
