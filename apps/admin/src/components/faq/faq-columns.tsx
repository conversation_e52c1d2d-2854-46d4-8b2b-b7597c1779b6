import { Link } from "@tanstack/react-router";
import { GetAllFaqsResponse } from "@vtuber/services/cms";
import { Badge } from "@vtuber/ui/components/badge";
import { buttonVariants } from "@vtuber/ui/components/button";
import { ColumnDef } from "@vtuber/ui/components/data-table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@vtuber/ui/components/dialog";
import { AnswerIcon } from "@vtuber/ui/components/icons/answer-icon";
import { QuestionIcon } from "@vtuber/ui/components/icons/question-icon";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { ChevronsUpDown, Pencil } from "lucide-react";
import { useState } from "react";
import { DeleteFaq } from "./delete-faq";
import { ToggleFaq } from "./toggle-faq";

export const faqColumns: ColumnDef<GetAllFaqsResponse["data"][0]>[] = [
  {
    accessorKey: "index",
    header: "Position",
  },
  {
    accessorKey: "language",
    header: "Language",
    cell: ({ row }) => {
      return (
        <p>{row.original.language === "ja-jp" ? "Japanese" : "English"}</p>
      );
    },
  },
  {
    accessorKey: "tag",
    cell: ({ row }) => <Badge variant={"success"}>{row.original.tag}</Badge>,
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      return (
        <ToggleFaq
          id={row.original.id}
          active={row.original.isActive}
        />
      );
    },
  },
  {
    accessorKey: "question",
    header: "Description",
    cell: ({ row }) => {
      const [isOpen, setIsOpen] = useState(false);

      return (
        <>
          <div
            className={buttonVariants({
              variant: "outline-dark",
              className: "cursor-pointer flex items-center gap-2",
            })}
            onClick={() => setIsOpen(true)}>
            {row.original.question}
            <ChevronsUpDown className="h-4 w-4" />
          </div>

          <Dialog
            open={isOpen}
            onOpenChange={setIsOpen}>
            <DialogContent className="p-0 overflow-hidden">
              <DialogHeader className="bg-sub/30 p-4">
                <DialogTitle className="leading-relaxed flex items-center gap-2 pr-10">
                  <div className="flex items-center size-12 justify-center bg-sub rounded-full">
                    <QuestionIcon />
                  </div>
                  <p className="flex-1"> {row.original.question}</p>
                </DialogTitle>
              </DialogHeader>
              <div className="p-4">
                <div className="flex items-center gap-2">
                  <div className="flex items-center size-12 justify-center bg-sub rounded-full">
                    <AnswerIcon />
                  </div>
                  <h2 className="text-lg font-semibold">Answer</h2>
                </div>
                <ScrollArea className="max-h-[70vh] flex flex-col overflow-y-auto pl-[55px] text-sm text-font">
                  <MarkDown markdown={row.original.response} />
                </ScrollArea>
              </div>
            </DialogContent>
          </Dialog>
        </>
      );
    },
  },
  {
    accessorKey: "id",
    header: "Action",
    cell: ({ row }) => {
      return (
        <div className="flex items-center start gap-3">
          <Link
            className={buttonVariants({
              variant: "accent",
              size: "icon",
            })}
            to="/faq/$id"
            params={{
              id: row.original.id.toString(),
            }}>
            <Pencil />
          </Link>
          <DeleteFaq id={row.original.id} />
        </div>
      );
    },
  },
];
