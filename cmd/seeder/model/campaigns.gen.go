package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameCampaign = "campaigns"

// Campaign mapped from table <campaigns>
type Campaign struct {
	ID                 int64             `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name               string            `gorm:"column:name;not null" json:"name"`
	Description        string            `gorm:"column:description;not null" json:"description"`
	ShortDescription   string            `gorm:"column:short_description;not null" json:"short_description"`
	Thumbnail          string            `gorm:"column:thumbnail;not null" json:"thumbnail"`
	StartDate          time.Time         `gorm:"column:start_date;not null" json:"start_date"`
	EndDate            time.Time         `gorm:"column:end_date;not null" json:"end_date"`
	TotalBudget        int32             `gorm:"column:total_budget;not null" json:"total_budget"`
	PromotionalMessage string            `gorm:"column:promotional_message;not null" json:"promotional_message"`
	SocialMediaLinks   string            `gorm:"column:social_media_links;default:{}" json:"social_media_links"`
	VtuberID           int64             `gorm:"column:vtuber_id;not null" json:"vtuber_id"`
	CreatedAt          time.Time         `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt          time.Time         `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt          gorm.DeletedAt    `gorm:"column:deleted_at" json:"deleted_at"`
	CampaignBanners    []CampaignBanner  `gorm:"foreignKey:CampaignID;references:ID" json:"campaign_banners"`
	CampaignVariants   []CampaignVariant `gorm:"foreignKey:CampaignID;references:ID" json:"campaign_variants"`
	Slug               string            `gorm:"column:slug;not null" json:"slug"`
	Category           []Category        `gorm:"many2many:campaign_categories;" json:"category"`
}

// TableName Campaign's table name
func (*Campaign) TableName() string {
	return TableNameCampaign
}
