syntax = "proto3";
package api.cms.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/cms/v1;cmsv1";

message UpdateStaticRequest {
  int64 id = 1; // @gotag: validate:"required"
  string value = 2; // @gotag: validate:"required"
}

message AddStaticRequest {
  string key = 1; // @gotag: validate:"required"
  string value = 2; // @gotag: validate:"required"
  string language = 3; // @gotag: validate:"required,oneof=en-us ja-jp"
}

message DeleteStaticRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message StaticResponse {
  int64 id = 1;
  string key = 2;
  string value = 3;
  string language = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message GetAllStaticRequest {
  string key = 1; // @gotag: validate:"required"
}

message GetAllStaticResponse {
  repeated StaticResponse data = 1;
}

message DeleteStaticResponse {
  bool success = 1;
  string message = 2;
}

service StaticService {
  rpc UpdateStaticResource(UpdateStaticRequest) returns (StaticResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.role == 'admin'"
    };
  }
  rpc GetAllStaticResource(GetAllStaticRequest) returns (GetAllStaticResponse) {}
  rpc AddStaticResource(AddStaticRequest) returns (StaticResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.role == 'admin'"
    };
  }
  rpc DeleteStaticResource(DeleteStaticRequest) returns (DeleteStaticResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.role == 'admin'"
    };
  }
}
