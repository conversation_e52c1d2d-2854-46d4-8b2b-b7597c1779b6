syntax = "proto3";

package api.content.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/content/v1;contentv1";

message AddPostRequest {
  string title = 1; // @gotag: validate:"required"
  string description = 2; // @gotag: validate:"required"
  optional string media = 3;
  optional string media_type = 4; // @gotag: validate:"omitempty,oneof=picture video"
  string name = 5;
  bool membership_only = 6;
  int64 category_id = 7; // @gotag: validate:"required"
  string short_description = 8; // @gotag: validate:"required"
}

message AddPostResponse {
  Post data = 1;
}

message Post {
  int64 id = 1;
  string title = 2;
  string description = 3;
  optional string media = 4;
  optional string media_type = 5;
  string name = 6;
  bool membership_only = 7;
  int64 category_id = 8;
  api.shared.v1.Profile vtuber = 9;
  google.protobuf.Timestamp created_at = 10;
  string short_description = 11;
  int64 post_likes = 12;
  bool has_liked = 13;
  int64 post_comments = 14;
  string slug = 15;
}

message GetAllPostsRequest {
  optional string vtuber_id = 1;
  optional int64 category_id = 2;
  optional api.shared.v1.PaginationRequest pagination = 3;
}

message GetAllPostsResponse {
  repeated Post data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetPostByIdRequest {
  string id = 1; // @gotag: validate:"required"
}

message GetPostByIdResponse {
  Post data = 1;
}

message DeletePostByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetMyPostsRequest {
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message UpdatePostByIdRequest {
  string title = 1; // @gotag: validate:"required"
  string description = 2; // @gotag: validate:"required"
  optional string media = 3;
  optional string media_type = 4; // @gotag: validate:"omitempty,oneof=picture video"
  string name = 5; // @gotag: validate:"required"
  bool membership_only = 6;
  int64 category_id = 7; // @gotag: validate:"required"
  int64 id = 9; // @gotag: validate:"required"
  string short_description = 10; // @gotag: validate:"required"
}

message GetVtuberGalleryRequest {
  int64 vtuber_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message DeletePostByIdResponse {
  bool success = 1;
  string message = 2;
}

message UpdatePostByIdResponse {
  bool success = 1;
  string message = 2;
}

service PostService {
  rpc AddPost(AddPostRequest) returns (AddPostResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetAllPosts(GetAllPostsRequest) returns (GetAllPostsResponse) {}
  rpc GetPostById(GetPostByIdRequest) returns (GetPostByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeletePostById(DeletePostByIdRequest) returns (DeletePostByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }

  rpc GetMyPosts(GetMyPostsRequest) returns (GetAllPostsResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }

  rpc UpdatePostById(UpdatePostByIdRequest) returns (UpdatePostByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }

  rpc GetVtuberGalleries(GetVtuberGalleryRequest) returns (GetAllPostsResponse) {}
}
