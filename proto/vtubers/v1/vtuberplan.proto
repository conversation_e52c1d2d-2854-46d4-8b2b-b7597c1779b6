syntax = "proto3";

package api.vtubers.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1";

message AddVtuberPlanRequest {
  string title = 2; // @gotag: validate:"required"
  string description = 3; // @gotag: validate:"required"
  int32 price = 4; // @gotag: validate:"gte=1"
  int32 index = 5; // @gotag: validate:"gte=1"
  string short_description = 6; // @gotag: validate:"required"
}

message AddVtuberPlanResponse {
  VtuberPlan data = 1;
}

message VtuberPlan {
  int64 id = 1;
  string title = 2;
  string description = 3;
  int32 price = 4;
  int64 vtuber_id = 5;
  int32 index = 6;
  google.protobuf.Timestamp created_at = 7;
  bool is_subscribed = 8;
  string short_description = 9;
}

message GetAllVtuberPlansByVtuberIdRequest {
  int64 vtuber_id = 1; // @gotag: validate:"required"
}

message GetAllVtuberPlansResponse {
  repeated VtuberPlan VtuberPlan = 1;
}

message GetVtuberPlanByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetVtuberPlanByIdResponse {
  VtuberPlan data = 1;
}

message DeleteVtuberPlanByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateVtuberPlanByIdRequest {
  string title = 1; // @gotag: validate:"required"
  string description = 2; // @gotag: validate:"required"
  int64 id = 3; // @gotag: validate:"required"
  int32 price = 4; // @gotag: validate:"gte=1"
  int32 index = 6; // @gotag: validate:"gte=1"
  string short_description = 7; // @gotag: validate:"required"
}

service VtuberPlanService {
  rpc AddVtuberPlan(AddVtuberPlanRequest) returns (AddVtuberPlanResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetAllVtuberPlansByVtuberId(GetAllVtuberPlansByVtuberIdRequest) returns (GetAllVtuberPlansResponse);
  rpc GetVtuberPlanById(GetVtuberPlanByIdRequest) returns (GetVtuberPlanByIdResponse);
  rpc DeleteVtuberPlanById(DeleteVtuberPlanByIdRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc UpdateVtuberPlanById(UpdateVtuberPlanByIdRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }
}
